import { ClsServiceManager } from 'nestjs-cls'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { BizException } from '@/common/exceptions/biz.exception'
import { UserModel } from '@/common/drizzle/schema/system-users'

export function getUserContext() {
  const cls = ClsServiceManager.getClsService()
  const user: UserModel = cls.get('user')
  if (!user) {
    throw new BizException(ErrorCodeEnum.LOGIN_FAILED)
  }
  return user
}

export function getUserId() {
  const user = getUserContext()
  return user.id
}
