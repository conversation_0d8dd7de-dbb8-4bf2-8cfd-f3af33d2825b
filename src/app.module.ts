import { Module } from '@nestjs/common'
import { APP_FILTER } from '@nestjs/core'
import { ThrottlerModule } from '@nestjs/throttler'
import { ClsModule } from 'nestjs-cls'
import { DrizzleModule } from './common/drizzle/drizzle.module'
import { AllExceptionsFilter } from './common/filters/any-exception.filter'
import { RedisModule } from './common/redis/redis.module'
import { LoggerModule } from './global/logger.module'
import { AuthModule } from './modules/auth/auth.module'
import { DouyinModule } from './modules/douyin/douyin.module'
import { SystemModule } from './modules/system/system.module'
import { TasksModule } from './shared/tasks/tasks.module'
import { TosModule } from './shared/tos/tos.module'
import { ProcessorModule } from './shared/processors/processors.module'

@Module({
  imports: [
    ClsModule.forRoot({
      global: true,
      middleware: { mount: true },
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60,
          limit: 10,
        },
      ],
    }),
    TasksModule,
    ProcessorModule,
    DrizzleModule,
    RedisModule,
    LoggerModule,

    // biz start
    AuthModule,
    SystemModule,
    DouyinModule,
    TosModule,
    // biz end
  ],
  providers: [
    {
      provide: APP_FILTER,
      useClass: AllExceptionsFilter,
    },
  ],
})
export class AppModule {}
