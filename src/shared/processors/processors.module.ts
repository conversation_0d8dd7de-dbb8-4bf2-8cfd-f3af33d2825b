import { Global, Logger, Module, OnModuleInit } from '@nestjs/common'
import { BullModule } from '@nestjs/bullmq'
import { REDIS_URL } from '@/app.config'
import { DOUYIN_DRAMA_QUEUE_NAME } from '@/constants/processor.contants'
import { DramaProcessor } from './drama.processor'

@Global()
@Module({
  imports: [
    BullModule.forRoot({
      connection: {
        url: REDIS_URL,
        db: 1,
      },
    }),
    BullModule.registerQueue({
      name: DOUYIN_DRAMA_QUEUE_NAME,
    }),
  ],
  providers: [DramaProcessor],
  exports: [BullModule],
})
export class ProcessorModule implements OnModuleInit {
  constructor(private readonly logger: Logger) {}

  onModuleInit() {
    this.logger.log('任务队列模块启动成功')
  }
}
