import { DOUYIN_DRAMA_QUEUE_NAME } from '@/constants/processor.contants'
import { Processor, WorkerHost } from '@nestjs/bullmq'
import { Job } from 'bullmq'

import { Logger } from '@nestjs/common'

function sleep(ms: number) {
  return new Promise((rs) => setTimeout(rs, ms))
}

@Processor(DOUYIN_DRAMA_QUEUE_NAME)
export class DramaProcessor extends WorkerHost {
  constructor(private readonly logger: Logger) {
    super()
  }

  async process(job: Job<any, any, string>): Promise<any> {
    switch (job.name) {
      case 'test':
        await sleep(1000)
        break
    }
  }
}
