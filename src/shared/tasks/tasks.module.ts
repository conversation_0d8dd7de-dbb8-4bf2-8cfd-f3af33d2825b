import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common'
import { TasksService } from './tasks.service'
import { ScheduleModule } from '@nestjs/schedule'

@Module({
  imports: [ScheduleModule.forRoot()],
  providers: [TasksService],
})
export class TasksModule implements OnModuleInit {
  constructor(private readonly logger: Logger) {}

  onModuleInit() {
    this.logger.log('定时任务模块启动成功')
  }
}
