import { TOS_ACCESS_KEY_ID, TOS_ACCESS_KEY_SECRET, TOS_BUCKET, TOS_ENDPOINT, TOS_REGION } from '@/app.config'
import { Injectable } from '@nestjs/common'
import { TosClient } from '@volcengine/tos-sdk'

@Injectable()
export class TosService {
  private readonly client: TosClient
  constructor() {
    this.client = new TosClient({
      accessKeyId: TOS_ACCESS_KEY_ID,
      accessKeySecret: TOS_ACCESS_KEY_SECRET,
      region: TOS_REGION,
      endpoint: TOS_ENDPOINT,
    })
  }

  async getUploadSign(key: string) {
    const result = await this.client.preSignedPostSignature({
      bucket: TOS_BUCKET,
      key,
      expiresIn: 3600,
    })
    return result
  }
}
