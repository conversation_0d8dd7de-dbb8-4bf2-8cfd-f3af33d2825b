import { IsIn, IsNotEmpty } from 'class-validator'
import { FileRequest } from './file.request'
import { FILE_SUFFIX } from './tos.enum'
import { Transform } from 'class-transformer'

export class UploadSignRequest extends FileRequest {
  @IsIn(FILE_SUFFIX, { message: '文件后缀不合法' })
  @IsNotEmpty({ message: '文件后缀不能为空' })
  @Transform(({ value }) => (value as string).toLowerCase())
  fileSuffix: string
}
