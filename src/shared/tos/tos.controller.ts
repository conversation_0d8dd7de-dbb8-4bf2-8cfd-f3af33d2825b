import { Body, Controller, Post } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { TosService } from './tos.service'
import { UploadSignRequest } from './requests/tos.request'
import KSUID from 'ksuid'
import { TOS_UPLOAD_DIR } from '@/app.config'
import { CommonResponse } from '@/common/responses/common.response'
import { Auth } from '@/common/decorators/auth.decorator'

@ApiTags('对象存储')
@Controller('tos')
export class TosController {
  constructor(private readonly tosService: TosService) {}

  // https://www.volcengine.com/docs/6349/129225
  @Post('/sign')
  @Auth()
  async getSign(@Body() uploadSignRequest: UploadSignRequest) {
    const { fileSuffix } = uploadSignRequest
    const key = await this.getUploadKey(fileSuffix)
    const sign = await this.tosService.getUploadSign(key)
    return CommonResponse.ok({ sign })
  }

  private async getUploadKey(suffix: string) {
    const ksuid = await KSUID.random()
    return `${TOS_UPLOAD_DIR}/${ksuid.string}.${suffix}`
  }
}
