import { IS_PRODUCTION } from './global/env.global'

export const DATABASE_URL = IS_PRODUCTION
  ? 'mysql://ugc_backend:<EMAIL>:3306/ugc'
  : 'mysql://root:jz130185@localhost:3306/ugc'
export const REDIS_URL = IS_PRODUCTION
  ? 'redis://default:<EMAIL>:6379'
  : 'redis://:jz130185@localhost:6379/0'

export const APP_NAME = 'ugc-service'
export const APP_PORT = 8000

export const LOGGER_DIR = 'logs'
export const LOGGER_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'
export const LOGGER_DATE_PATTERN = 'YYYY-MM-DD-HH'
export const LOGGER_CHUNK_SIZE = '20m'
export const LOGGER_RESERVE_TIME = '14d'

export const JWT_SECRET = 'ugc-service'
export const JWT_EXPIRE = '30d'

export const CACHE_DEFAULT_TTL = 60 * 60 * 24 * 30

// 火山引擎相关
export const VOLCENGINE_ACCESS_KEY_ID = 'AKLTMzg4ODk4MWJkZTJiNGExYTkxNzEzYTc2NTcyODE3NzY'
export const VOLCENGINE_SECRET_ACCESS_KEY = 'TVRFd1lUQTVaVFJoTkdRME5HVmpZV0k1WlRrek5UaG1NREV5WW1Wa056SQ'

// 小程序相关
export const DY_APP_ID = 'tt410237bfde091ebd01'
export const DY_APP_SECRET = 'd20582e2414284d39b956a32c48b033763797ded'

// 短信相关
export const SMS_ACCOUNT = '836c3d74'
export const SMS_SIGN = '羚羊漫剧'
export const SMS_TEMPLATE_ID = 'SPT_09a29a26'
export const SMS_CACHE_TIME = 60 * 1000 * 5

// 对象存储相关
export const TOS_ACCESS_KEY_ID = 'AKLTYTk5ZDE0YjUzMmUxNDE1Zjk3Y2U1MGE0NTU1YzI4ZjA'
export const TOS_ACCESS_KEY_SECRET = 'WVRWallqZzJOR0pqWW1RMU5HTTFaR0pqTWpkbVkyUmlOelV4TkRnNE5UQQ=='
export const TOS_REGION = 'cn-beijing'
export const TOS_ENDPOINT = 'tos-cn-beijing.volces.com'
export const TOS_BUCKET = 'tt410237bfde091ebd01-env-koltfrrp9o'
export const TOS_UPLOAD_DIR = 'upload'
