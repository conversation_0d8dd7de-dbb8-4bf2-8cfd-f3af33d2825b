import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString, IsIn, IsInt, Min, Max } from 'class-validator'
import { Transform } from 'class-transformer'

export class DouyinLoginRequest {
  @ApiProperty({ description: '抖音小程序的 anonymousCode' })
  anonymousCode: string

  @ApiProperty({ description: '抖音小程序的 jsCode' })
  code: string
}

export class DramaRankingRequest {
  @ApiProperty({
    description: '排行榜类型',
    enum: ['hot', 'must_watch'],
    example: 'hot',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['hot', 'must_watch'])
  type?: string = 'hot'

  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(String(value)))
  page?: number = 1

  @ApiProperty({ description: '每页数量', example: 20, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform(({ value }) => Math.min(parseInt(String(value)) || 20, 50))
  size?: number = 20
}

export class DramaListRequest {
  @ApiProperty({ description: '分类ID', required: false })
  @IsOptional()
  @IsString()
  categoryId?: string

  @ApiProperty({ description: '短剧标题（模糊搜索）', required: false })
  @IsOptional()
  @IsString()
  title?: string

  @ApiProperty({
    description: '短剧状态',
    enum: [1, 2, 3],
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @IsIn([1, 2, 3])
  @Transform(({ value }) => parseInt(String(value)))
  albumStatus?: number

  @ApiProperty({ description: '发行年份', required: false })
  @IsOptional()
  @IsInt()
  @Min(1900)
  @Max(2100)
  @Transform(({ value }) => parseInt(String(value)))
  year?: number

  @ApiProperty({ description: '题材标签（模糊搜索）', required: false })
  @IsOptional()
  @IsString()
  tagList?: string

  @ApiProperty({
    description: '是否免费试看',
    enum: [0, 1],
    example: 0,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @IsIn([0, 1])
  @Transform(({ value }) => parseInt(String(value)))
  freePreview?: number

  @ApiProperty({
    description: '排序字段',
    enum: ['createdAt', 'year', 'seqNum', 'originalPrice'],
    example: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['createdAt', 'year', 'seqNum', 'originalPrice'])
  orderBy?: string = 'createdAt'

  @ApiProperty({
    description: '排序方式',
    enum: ['asc', 'desc'],
    example: 'desc',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  order?: 'asc' | 'desc' = 'desc'

  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(String(value)))
  page?: number = 1

  @ApiProperty({ description: '每页数量', example: 20, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform(({ value }) => Math.min(parseInt(String(value)) || 20, 50))
  size?: number = 20
}
