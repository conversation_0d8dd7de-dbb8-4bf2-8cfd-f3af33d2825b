import { ApiProperty } from '@nestjs/swagger'
import { IsOptional, IsString, IsIn, IsInt, Min, Max } from 'class-validator'
import { Transform } from 'class-transformer'

export class DouyinLoginRequest {
  @ApiProperty({ description: '抖音小程序的 anonymousCode' })
  anonymousCode: string

  @ApiProperty({ description: '抖音小程序的 jsCode' })
  code: string
}

export class DramaRankingRequest {
  @ApiProperty({
    description: '排行榜类型',
    enum: ['hot', 'must_watch'],
    example: 'hot',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['hot', 'must_watch'])
  type?: string = 'hot'

  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Transform(({ value }) => parseInt(String(value)))
  page?: number = 1

  @ApiProperty({ description: '每页数量', example: 20, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(50)
  @Transform(({ value }) => Math.min(parseInt(String(value)) || 20, 50))
  size?: number = 20
}
