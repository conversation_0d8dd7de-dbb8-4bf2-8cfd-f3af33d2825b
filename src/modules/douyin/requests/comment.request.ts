import { ApiProperty } from '@nestjs/swagger'
import { IsInt, IsNotEmpty, IsOptional, IsString, MaxLength, Min } from 'class-validator'

// 查询分集评论列表请求
export class DouyinEpisodeCommentsRequest {
  @ApiProperty({ description: '短剧ID' })
  @IsNotEmpty({ message: '短剧ID不能为空' })
  @IsString()
  @MaxLength(100)
  dramaId: string

  @ApiProperty({ description: '分集ID' })
  @IsNotEmpty({ message: '分集ID不能为空' })
  @IsString()
  @MaxLength(100)
  episodeId: string

  @ApiProperty({ description: '页码', example: 1, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number = 1

  @ApiProperty({ description: '每页数量', example: 20, required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  size?: number = 20
}

// 创建评论请求
export class DouyinCreateCommentRequest {
  @ApiProperty({ description: '短剧ID' })
  @IsNotEmpty({ message: '短剧ID不能为空' })
  @IsString()
  @MaxLength(100)
  dramaId: string

  @ApiProperty({ description: '分集ID' })
  @IsNotEmpty({ message: '分集ID不能为空' })
  @IsString()
  @MaxLength(100)
  episodeId: string

  @ApiProperty({ description: '评论内容' })
  @IsNotEmpty({ message: '评论内容不能为空' })
  @IsString()
  @MaxLength(1000)
  content: string

  @ApiProperty({ description: '父评论ID（回复评论时需要）', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  parentId?: string

  @ApiProperty({ description: '回复目标用户ID（回复评论时需要）', required: false })
  @IsOptional()
  @IsString()
  @MaxLength(100)
  replyToUserId?: string
}

// 点赞评论请求
export class DouyinLikeCommentRequest {
  @ApiProperty({ description: '评论ID' })
  @IsNotEmpty({ message: '评论ID不能为空' })
  @IsString()
  @MaxLength(100)
  commentId: string

  @ApiProperty({ description: '点赞状态：0-取消点赞 1-点赞', example: 1 })
  @IsNotEmpty({ message: '点赞状态不能为空' })
  @IsInt()
  @Min(0)
  status: number
}

// 删除评论请求
export class DouyinDeleteCommentRequest {
  @ApiProperty({ description: '评论ID' })
  @IsNotEmpty({ message: '评论ID不能为空' })
  @IsString()
  @MaxLength(100)
  commentId: string
}
