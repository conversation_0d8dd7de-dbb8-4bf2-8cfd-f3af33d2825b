import { Controller, Get, Query } from '@nestjs/common'
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import { CommonResponse } from '@/common/responses/common.response'
import { DouyinDramaService } from '../services/drama.service'
import { DramaRankingRequest } from '../requests/douyin.request'
import { DramaRankingResponse } from '../responses/drama.response'

@ApiTags('抖音小程序-短剧')
@Controller('/dy/drama')
export class DouyinDramaController {
  constructor(private readonly douyinDramaService: DouyinDramaService) {}

  @ApiOperation({
    summary: '获取短剧排行榜',
    description: '获取短剧排行榜，支持热播榜（近3个月销量）和必看榜（历史销量）',
  })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: DramaRankingResponse,
    isArray: true,
  })
  @Get('/ranking')
  async getDramaRanking(@Query() request: DramaRankingRequest) {
    const result = await this.douyinDramaService.getDramaRanking(request)
    return CommonResponse.ok(result)
  }
}
