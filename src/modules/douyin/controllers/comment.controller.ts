import { Auth } from '@/common/decorators/auth.decorator'
import { CurrentUser } from '@/common/decorators/current-user.decorator'
import { UserModel } from '@/common/drizzle/schema/system-users'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { DouyinCommentService } from '../services/comment.service'
import {
  DouyinEpisodeCommentsRequest,
  DouyinCreateCommentRequest,
  DouyinLikeCommentRequest,
  DouyinDeleteCommentRequest,
} from '../requests/comment.request'

@ApiTags('抖音小程序-评论')
@Controller('/dy/comment')
export class DouyinCommentController {
  constructor(private readonly douyinCommentService: DouyinCommentService) {}

  @ApiOperation({ summary: '获取分集评论列表' })
  @Get('/episode')
  async getEpisodeComments(@Query() dto: DouyinEpisodeCommentsRequest, @CurrentUser() user?: UserModel) {
    const result = await this.douyinCommentService.getEpisodeComments(dto, user?.id)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '发表评论' })
  @Auth()
  @Post()
  async createComment(@Body() dto: DouyinCreateCommentRequest, @CurrentUser() user: UserModel) {
    const commentId = await this.douyinCommentService.createComment(dto, user.id)
    return CommonResponse.ok({ commentId })
  }

  @ApiOperation({ summary: '点赞/取消点赞评论' })
  @Auth()
  @Post('/like')
  async likeComment(@Body() dto: DouyinLikeCommentRequest, @CurrentUser() user: UserModel) {
    const result = await this.douyinCommentService.likeComment(dto, user.id)
    return CommonResponse.ok({ success: result })
  }

  @ApiOperation({ summary: '删除评论（级联删除子评论）' })
  @Auth()
  @Delete()
  async deleteComment(@Body() dto: DouyinDeleteCommentRequest, @CurrentUser() user: UserModel) {
    const result = await this.douyinCommentService.deleteComment(dto, user.id)
    return CommonResponse.ok({ success: result })
  }
}
