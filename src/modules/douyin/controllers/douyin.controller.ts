import { Auth } from '@/common/decorators/auth.decorator'
import { CurrentUser } from '@/common/decorators/current-user.decorator'
import { UserModel } from '@/common/drizzle/schema/system-users'
import { UserIpInterceptor } from '@/common/interceptors/user-ip.interceptor'
import { CommonResponse } from '@/common/responses/common.response'
import { toResponse } from '@/common/utils/transform.util'
import { AuthService } from '@/modules/auth/auth.service'
import { RoleEnum } from '@/modules/auth/interfaces/jwt-payload.interface'
import { Body, Controller, Get, Logger, Post, UseInterceptors } from '@nestjs/common'
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger'
import { DouyinLoginRequest } from '../requests/douyin.request'
import { UserInfoResponse } from '../responses/user.response'
import { DouyinService } from '../services/douyin.service'
import { DouyinClientProvider } from '@/modules/system/providers/douyin-client.provider'

@ApiTags('抖音小程序')
@Controller('/dy/user')
export class DouyinController {
  constructor(
    private readonly douyinService: DouyinService,
    private readonly authService: AuthService,
    private readonly logger: Logger,
    private readonly douyinClient: DouyinClientProvider,
  ) {}

  @ApiOperation({ summary: '抖音小程序登录' })
  @ApiResponse({ status: 200, description: '登录成功' })
  @UseInterceptors(UserIpInterceptor)
  @Post('/login')
  async login(@Body() loginRequest: DouyinLoginRequest) {
    const { anonymousCode, code } = loginRequest
    const user = await this.douyinService.login(anonymousCode, code)
    const token = await this.authService.signToken(user.id, RoleEnum.USER)
    this.logger.log(`用户登录成功，用户 ID: ${user.id}`)
    return CommonResponse.ok({ token })
  }

  @ApiOperation({ summary: '获取用户信息' })
  @Get()
  @Auth()
  getUserInfo(@CurrentUser() user: UserModel) {
    const userInfo = toResponse(UserInfoResponse, user)
    return CommonResponse.ok(userInfo)
  }

  @ApiOperation({ summary: '获取抖音小程序 OAuth 客户端 token' })
  @Get('/oauth/client/token')
  async getOAuthClientToken() {
    const token = await this.douyinClient.getOAuthClientToken()
    return CommonResponse.ok(token)
  }
}
