import { Controller, Get, Query } from '@nestjs/common'
import { DouyinCategoryService } from '../services/category.service'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { CommonResponse } from '@/common/responses/common.response'

@ApiTags('抖音小程序')
@Controller('/dy/category')
export class CategoryController {
  constructor(private readonly douyinCategoryService: DouyinCategoryService) {}

  @ApiOperation({ summary: '获取首页分类列表' })
  @Get()
  async list(@Query() paginationRequest: PaginationRequest) {
    const pageData = await this.douyinCategoryService.getHomePageCategoryList(paginationRequest)
    return CommonResponse.ok(pageData)
  }
}
