import { Modu<PERSON> } from '@nestjs/common'
import { DouyinService } from './services/douyin.service'
import { DouyinController } from './controllers/douyin.controller'
import { DouyinCategoryService } from './services/category.service'
import { CategoryController } from './controllers/category.controller'
import { DouyinCommentService } from './services/comment.service'
import { <PERSON>uyinCommentController } from './controllers/comment.controller'
import { DouyinDramaService } from './services/drama.service'
import { DouyinDramaController } from './controllers/drama.controller'
import { SystemModule } from '@/modules/system/system.module'

@Module({
  imports: [SystemModule],
  controllers: [DouyinController, CategoryController, DouyinCommentController, DouyinDramaController],
  providers: [DouyinService, DouyinCategoryService, DouyinCommentService, DouyinDramaService],
})
export class DouyinModule {}
