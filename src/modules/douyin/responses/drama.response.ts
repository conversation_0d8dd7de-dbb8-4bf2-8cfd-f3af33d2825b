import { ApiProperty } from '@nestjs/swagger'
import { Exclude, Expose } from 'class-transformer'

export class DramaRankingResponse {
  @ApiProperty({ description: '短剧ID' })
  @Expose()
  id: string

  @ApiProperty({ description: '短剧标题' })
  @Expose()
  title: string

  @ApiProperty({ description: '竖版封面图片URL' })
  @Expose()
  coverVertical: string

  @ApiProperty({ description: '横版封面图片URL' })
  @Expose()
  coverHorizontal: string

  @ApiProperty({ description: '短剧简介' })
  @Expose()
  desp: string

  @ApiProperty({ description: '推荐语' })
  @Expose()
  recommendation: string

  @ApiProperty({ description: '总集数' })
  @Expose()
  seqNum: number

  @ApiProperty({ description: '发行年份' })
  @Expose()
  year: number

  @ApiProperty({ description: '题材标签' })
  @Expose()
  tagList: string

  @ApiProperty({ description: '分类ID' })
  @Expose()
  categoryId: string

  @ApiProperty({ description: '短剧状态: 1-未上映, 2-更新中, 3-已完结' })
  @Expose()
  albumStatus: number

  @ApiProperty({ description: '是否免费试看: 0-否, 1-是' })
  @Exclude()
  freePreview: number

  @ApiProperty({ description: '排名' })
  @Expose()
  rank: number
}
