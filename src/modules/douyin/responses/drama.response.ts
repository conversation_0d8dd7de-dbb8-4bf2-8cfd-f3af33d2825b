import { ApiProperty } from '@nestjs/swagger'
import { Exclude, Expose } from 'class-transformer'

export class DramaRankingResponse {
  @ApiProperty({ description: '短剧ID' })
  @Expose()
  id: string

  @ApiProperty({ description: '短剧标题' })
  @Expose()
  title: string

  @ApiProperty({ description: '竖版封面图片URL' })
  @Expose()
  coverVertical: string

  @ApiProperty({ description: '横版封面图片URL' })
  @Expose()
  coverHorizontal: string

  @ApiProperty({ description: '短剧简介' })
  @Expose()
  desp: string

  @ApiProperty({ description: '推荐语' })
  @Expose()
  recommendation: string

  @ApiProperty({ description: '总集数' })
  @Expose()
  seqNum: number

  @ApiProperty({ description: '发行年份' })
  @Expose()
  year: number

  @ApiProperty({ description: '题材标签' })
  @Expose()
  tagList: string

  @ApiProperty({ description: '分类ID' })
  @Expose()
  categoryId: string

  @ApiProperty({ description: '短剧状态: 1-未上映, 2-更新中, 3-已完结' })
  @Expose()
  albumStatus: number

  @ApiProperty({ description: '原价' })
  @Expose()
  originalPrice: string

  @ApiProperty({ description: '优惠价' })
  @Expose()
  discountPrice: string

  @ApiProperty({ description: '是否免费试看: 0-否, 1-是' })
  @Expose()
  freePreview: number

  @ApiProperty({ description: '货币单位' })
  @Expose()
  currency: string

  @ApiProperty({ description: '排名' })
  @Expose()
  rank: number

  @ApiProperty({ description: '销量' })
  @Expose()
  salesCount: number

  // 排除敏感字段
  @Exclude()
  albumId: string

  @Exclude()
  coverVerticalOpenPicId: string

  @Exclude()
  coverHorizontalOpenPicId: string

  @Exclude()
  qualification: number

  @Exclude()
  duration: number

  @Exclude()
  productionOrganisation: string

  @Exclude()
  director: string

  @Exclude()
  producer: string

  @Exclude()
  actor: string

  @Exclude()
  costDistributionUri: string

  @Exclude()
  assuranceUri: string

  @Exclude()
  playletProductionCost: number

  @Exclude()
  screenWriter: string

  @Exclude()
  recordType: number

  @Exclude()
  broadcastRecordNumber: string

  @Exclude()
  licenseNum: string

  @Exclude()
  registrationNum: string

  @Exclude()
  ordinaryRecordNum: string

  @Exclude()
  keyRecordNum: string

  @Exclude()
  introImages: string

  @Exclude()
  introImagesOpenPicId: string

  @Exclude()
  douyinAuditStatus: string

  @Exclude()
  douyinPublishStatus: string

  @Exclude()
  platformAuditStatus: string

  @Exclude()
  version: number

  @Exclude()
  auditSuccessVersion: number

  @Exclude()
  auditMsg: string

  @Exclude()
  scopeList: string

  @Exclude()
  lastAuditTime: string

  @Exclude()
  onlineVersion: number

  @Exclude()
  onlineStatus: number

  @Exclude()
  onlineTime: string

  @Exclude()
  totalSalesAmount: string

  @Exclude()
  totalSalesCount: number

  @Exclude()
  lastDaySales: number

  @Exclude()
  last7DaysSales: number

  @Exclude()
  last30DaysSales: number

  @Exclude()
  yearSales: number

  @Exclude()
  publishTime: string

  @Exclude()
  lastSaleTime: string

  @Exclude()
  isInRecommendPool: number

  @Exclude()
  recommendPoolEnterTime: string

  @Exclude()
  authorId: string

  @Exclude()
  createdAt: string

  @Exclude()
  updatedAt: string

  @Exclude()
  isDeleted: number

  @Exclude()
  summary: string
}
