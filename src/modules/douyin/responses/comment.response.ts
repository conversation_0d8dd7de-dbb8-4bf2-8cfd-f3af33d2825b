import { BaseResponse } from '@/common/responses/base.response'

// 抖音评论响应
export class DouyinCommentResponse extends BaseResponse {
  id: string
  userId: string
  dramaId: string
  episodeId: string
  content: string
  parentId?: string
  replyToUserId?: string
  likeCount: number
  replyCount: number
  status: number
  isFeatured: number
  isLiked: boolean // 当前用户是否已点赞

  user?: {
    id: string
    username: string
    nickname?: string
    avatar?: string
  }

  episode?: {
    id: string
    title: string
    seq: number
  }

  replyToUser?: {
    id: string
    username: string
    nickname?: string
  }

  // 子评论列表（回复）
  replies?: DouyinCommentResponse[]
}

// 分集评论列表响应
export class DouyinEpisodeCommentsResponse {
  list: DouyinCommentResponse[]
  total: number
  page: number
  size: number
  hasMore: boolean
}
