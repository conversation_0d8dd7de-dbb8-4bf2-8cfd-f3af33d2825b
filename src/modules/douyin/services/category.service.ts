import { DrizzleService } from '@/common/drizzle/database.provider'
import { withPagination } from '@/common/drizzle/drizzle.helper'
import { categories } from '@/common/drizzle/schema'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { toResponse } from '@/common/utils/transform.util'
import { pageQuery } from '@/common/utils/page-query.util'
import { Injectable } from '@nestjs/common'
import { and, SQLWrapper } from 'drizzle-orm'
import { DouyinCategoryResponse } from '../responses/category.response'

@Injectable()
export class DouyinCategoryService {
  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * 获取小程序首页顶部的分类列表
   */
  async getHomePageCategoryList(paginationRequest: PaginationRequest) {
    const { baseQuery, countQuery } = pageQuery(this.drizzle.db, categories)

    const wheres: S<PERSON><PERSON>rapper[] = []

    baseQuery.where(and(...wheres))
    countQuery.where(and(...wheres))

    const [data, [{ count: total }]] = await Promise.all([
      withPagination(baseQuery, paginationRequest.page, paginationRequest.size),
      countQuery,
    ])

    const list = toResponse(DouyinCategoryResponse, data)

    return PaginationResponse.fromPaginationRequest(list, total, paginationRequest)
  }
}
