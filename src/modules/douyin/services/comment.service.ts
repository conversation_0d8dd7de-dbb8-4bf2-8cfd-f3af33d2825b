import { Injectable } from '@nestjs/common'
import { Dr<PERSON>zleService, DrizzleDB } from '@/common/drizzle/database.provider'
import { comments as commentsTable, commentLikes, dramas, episodes, systemUsers } from '@/common/drizzle/schema'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { eq, and, desc, sql, isNull, inArray } from 'drizzle-orm'
import { toResponse } from '@/common/utils/transform.util'
import { LogicDelete } from '@/constants/system.constants'
import {
  DouyinEpisodeCommentsRequest,
  DouyinCreateCommentRequest,
  DouyinLikeCommentRequest,
  DouyinDeleteCommentRequest,
} from '../requests/comment.request'
import { DouyinCommentResponse, DouyinEpisodeCommentsResponse } from '../responses/comment.response'

@Injectable()
export class DouyinCommentService {
  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * 验证用户是否存在
   */
  private async validateUser(userId: string): Promise<void> {
    const user = await this.drizzle.db.query.systemUsers.findFirst({
      where: eq(systemUsers.id, userId),
      columns: { id: true },
    })
    if (!user) {
      throw new BizException(ErrorCodeEnum.USER_NOT_FOUND)
    }
  }

  /**
   * 验证短剧和分集是否存在
   */
  private async validateDramaAndEpisode(dramaId: string, episodeId: string): Promise<void> {
    // 验证短剧是否存在
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: eq(dramas.id, dramaId),
      columns: { id: true },
    })
    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 验证分集是否存在且属于该短剧
    const episode = await this.drizzle.db.query.episodes.findFirst({
      where: and(eq(episodes.id, episodeId), eq(episodes.dramaId, dramaId)),
      columns: { id: true },
    })
    if (!episode) {
      throw new BizException(ErrorCodeEnum.EPISODE_NOT_FOUND)
    }
  }

  /**
   * 验证父级评论是否存在
   */
  private async validateParentComment(parentId: string): Promise<void> {
    const parentComment = await this.drizzle.db.query.comments.findFirst({
      where: and(eq(commentsTable.id, parentId), eq(commentsTable.isDeleted, LogicDelete.NotDeleted)),
      columns: { id: true },
    })
    if (!parentComment) {
      throw new BizException(ErrorCodeEnum.COMMENT_NOT_FOUND)
    }
  }

  /**
   * 验证评论是否存在
   */
  private async validateComment(commentId: string): Promise<void> {
    const comment = await this.drizzle.db.query.comments.findFirst({
      where: and(eq(commentsTable.id, commentId), eq(commentsTable.isDeleted, LogicDelete.NotDeleted)),
      columns: { id: true },
    })
    if (!comment) {
      throw new BizException(ErrorCodeEnum.COMMENT_NOT_FOUND)
    }
  }

  /**
   * 完整的创建评论数据验证
   */
  private async validateCreateCommentData(dto: DouyinCreateCommentRequest, userId: string): Promise<void> {
    // 验证当前用户是否存在
    await this.validateUser(userId)

    // 验证短剧和分集是否存在
    await this.validateDramaAndEpisode(dto.dramaId, dto.episodeId)

    // 验证父级评论是否存在（如果有）
    if (dto.parentId) {
      await this.validateParentComment(dto.parentId)
    }

    // 验证被回复用户是否存在（如果有）
    if (dto.replyToUserId) {
      await this.validateUser(dto.replyToUserId)
    }
  }

  /**
   * 获取分集评论列表
   * 精选评论排在前面，按点赞数和时间排序
   */
  async getEpisodeComments(
    dto: DouyinEpisodeCommentsRequest,
    currentUserId?: string,
  ): Promise<DouyinEpisodeCommentsResponse> {
    const { dramaId, episodeId, page = 1, size = 20 } = dto

    // 验证短剧和分集是否存在
    await this.validateDramaAndEpisode(dramaId, episodeId)

    // 如果提供了用户ID，验证用户是否存在
    if (currentUserId) {
      await this.validateUser(currentUserId)
    }

    const offset = (page - 1) * size

    // 查询主评论（非回复）
    const mainComments = await this.drizzle.db.query.comments.findMany({
      where: and(
        eq(commentsTable.dramaId, dramaId),
        eq(commentsTable.episodeId, episodeId),
        isNull(commentsTable.parentId), // 只查询主评论
        eq(commentsTable.isDeleted, LogicDelete.NotDeleted),
        eq(commentsTable.status, 1), // 只查询已发布的评论
      ),
      limit: size,
      offset,
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            nickname: true,
            avatar: true,
          },
        },
        episode: {
          columns: {
            id: true,
            title: true,
            seq: true,
          },
        },
        replyToUser: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
      // 排序规则：精选评论在前，然后按点赞数降序，点赞数相同按时间升序
      orderBy: [desc(commentsTable.isFeatured), desc(commentsTable.likeCount), commentsTable.createdAt],
    })

    // 获取总数
    const countResult = await this.drizzle.db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(commentsTable)
      .where(
        and(
          eq(commentsTable.dramaId, dramaId),
          eq(commentsTable.episodeId, episodeId),
          isNull(commentsTable.parentId),
          eq(commentsTable.isDeleted, LogicDelete.NotDeleted),
          eq(commentsTable.status, 1),
        ),
      )

    const total = countResult[0]?.count ?? 0

    // 获取当前用户的点赞状态
    const commentsWithLikeStatus = await this.addLikeStatus(mainComments, currentUserId)

    // 获取每个主评论的回复
    const commentsWithReplies = await this.addReplies(commentsWithLikeStatus, currentUserId)

    const list = toResponse(DouyinCommentResponse, commentsWithReplies)

    return {
      list,
      total,
      page,
      size,
      hasMore: page * size < total,
    }
  }

  /**
   * 创建评论
   */
  async createComment(dto: DouyinCreateCommentRequest, userId: string): Promise<string> {
    // 完整的数据验证
    await this.validateCreateCommentData(dto, userId)

    return await this.drizzle.db.transaction(async (tx) => {
      const commentData = {
        userId,
        dramaId: dto.dramaId,
        episodeId: dto.episodeId,
        content: dto.content,
        parentId: dto.parentId,
        replyToUserId: dto.replyToUserId,
        likeCount: 0,
        replyCount: 0,
        status: 1, // 直接发布，不需要审核
        isFeatured: 0,
      }

      const [{ id }] = await tx.insert(commentsTable).values(commentData).$returningId()

      // 如果是回复评论，更新父评论的回复数
      if (dto.parentId) {
        await tx
          .update(commentsTable)
          .set({
            replyCount: sql`${commentsTable.replyCount} + 1`,
          })
          .where(eq(commentsTable.id, dto.parentId))
      }

      return id
    })
  }

  /**
   * 点赞/取消点赞评论
   */
  async likeComment(dto: DouyinLikeCommentRequest, userId: string): Promise<boolean> {
    const { commentId, status } = dto

    // 验证用户和评论是否存在
    await this.validateUser(userId)
    await this.validateComment(commentId)

    return await this.drizzle.db.transaction(async (tx) => {
      // 查询是否已存在点赞记录
      const existingLike = await tx.query.commentLikes.findFirst({
        where: and(eq(commentLikes.userId, userId), eq(commentLikes.commentId, commentId)),
      })

      let likeCountChange = 0

      if (existingLike) {
        // 计算点赞数变化
        if (existingLike.status !== status) {
          likeCountChange = status === 1 ? 1 : -1
        }

        // 更新点赞状态
        await tx.update(commentLikes).set({ status }).where(eq(commentLikes.id, existingLike.id))
      } else {
        // 创建新的点赞记录
        await tx.insert(commentLikes).values({
          userId,
          commentId,
          status,
        })

        // 新记录的点赞数变化
        likeCountChange = status === 1 ? 1 : 0
      }

      // 更新评论的点赞数
      if (likeCountChange !== 0) {
        if (likeCountChange > 0) {
          // 增加点赞数
          await tx
            .update(commentsTable)
            .set({
              likeCount: sql`${commentsTable.likeCount} + ${likeCountChange}`,
            })
            .where(eq(commentsTable.id, commentId))
        } else {
          // 减少点赞数
          await tx
            .update(commentsTable)
            .set({
              likeCount: sql`GREATEST(${commentsTable.likeCount} + ${likeCountChange}, 0)`,
            })
            .where(eq(commentsTable.id, commentId))
        }
      }

      return true
    })
  }

  /**
   * 删除评论（级联删除子评论）
   */
  async deleteComment(dto: DouyinDeleteCommentRequest, userId: string): Promise<boolean> {
    const { commentId } = dto

    // 验证用户和评论是否存在
    await this.validateUser(userId)
    await this.validateComment(commentId)

    return await this.drizzle.db.transaction(async (tx) => {
      // 首先验证评论是否存在且属于当前用户
      const comment = await tx.query.comments.findFirst({
        where: and(
          eq(commentsTable.id, commentId),
          eq(commentsTable.userId, userId),
          eq(commentsTable.isDeleted, LogicDelete.NotDeleted),
        ),
      })

      if (!comment) {
        return false // 评论不存在或不属于当前用户
      }

      // 级联删除：先删除所有子评论
      await this.cascadeDeleteRepliesWithTx(tx, commentId)

      // 删除主评论
      await tx.update(commentsTable).set({ isDeleted: LogicDelete.Deleted }).where(eq(commentsTable.id, commentId))

      // 如果是回复评论，需要更新父评论的回复数
      if (comment.parentId) {
        await tx
          .update(commentsTable)
          .set({
            replyCount: sql`GREATEST(${commentsTable.replyCount} - 1, 0)`,
          })
          .where(eq(commentsTable.id, comment.parentId))
      }

      return true
    })
  }

  /**
   * 级联删除所有子评论（带事务支持）
   */
  private async cascadeDeleteRepliesWithTx(
    tx: Parameters<Parameters<DrizzleDB['transaction']>[0]>[0],
    parentId: string,
  ): Promise<void> {
    // 查找所有直接子评论
    const childComments = await tx.query.comments.findMany({
      where: and(eq(commentsTable.parentId, parentId), eq(commentsTable.isDeleted, LogicDelete.NotDeleted)),
      columns: {
        id: true,
      },
    })

    if (childComments.length === 0) {
      return
    }

    // 递归删除每个子评论的子评论
    for (const child of childComments) {
      await this.cascadeDeleteRepliesWithTx(tx, child.id)
    }

    // 删除所有直接子评论
    const childIds = childComments.map((c: { id: string }) => c.id)
    await tx.update(commentsTable).set({ isDeleted: LogicDelete.Deleted }).where(inArray(commentsTable.id, childIds))
  }

  /**
   * 添加用户点赞状态
   */
  private async addLikeStatus(
    comments: Array<Record<string, any>>,
    currentUserId?: string,
  ): Promise<Array<Record<string, any>>> {
    if (!currentUserId || comments.length === 0) {
      return comments.map((comment) => ({ ...comment, isLiked: false }))
    }

    const commentIds = comments.map((c) => String(c.id))
    const likes = await this.drizzle.db.query.commentLikes.findMany({
      where: and(
        eq(commentLikes.userId, currentUserId),
        inArray(commentLikes.commentId, commentIds),
        eq(commentLikes.status, 1),
      ),
    })

    const likedCommentIds = new Set(likes.map((like) => like.commentId))

    return comments.map((comment) => ({
      ...comment,
      isLiked: likedCommentIds.has(String(comment.id)),
    }))
  }

  /**
   * 添加回复评论
   */
  private async addReplies(
    comments: Array<Record<string, any>>,
    currentUserId?: string,
  ): Promise<Array<Record<string, any>>> {
    if (comments.length === 0) return comments

    const commentIds = comments.map((c) => String(c.id))

    // 查询所有回复
    const replies = await this.drizzle.db.query.comments.findMany({
      where: and(
        inArray(commentsTable.parentId, commentIds),
        eq(commentsTable.isDeleted, LogicDelete.NotDeleted),
        eq(commentsTable.status, 1),
      ),
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            nickname: true,
            avatar: true,
          },
        },
        replyToUser: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
      orderBy: (commentsTable, { desc }) => [desc(commentsTable.likeCount), commentsTable.createdAt],
    })

    // 添加回复的点赞状态
    const repliesWithLikeStatus = await this.addLikeStatus(replies, currentUserId)

    // 按父评论ID分组
    const repliesByParentId: Record<string, Array<Record<string, any>>> = {}
    repliesWithLikeStatus.forEach((reply) => {
      const parentId = String(reply.parentId)
      if (!repliesByParentId[parentId]) {
        repliesByParentId[parentId] = []
      }
      repliesByParentId[parentId].push(reply)
    })

    // 为每个主评论添加回复
    return comments.map((comment) => ({
      ...comment,
      replies: repliesByParentId[String(comment.id)] || [],
    }))
  }
}
