import { Injectable } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { dramas, orders } from '@/common/drizzle/schema'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { toResponse } from '@/common/utils/transform.util'
import { and, count, desc, eq, gte } from 'drizzle-orm'
import { DramaRankingRequest } from '../requests/douyin.request'
import { DramaRankingResponse } from '../responses/drama.response'
import { LogicDelete } from '@/constants/system.constants'

interface DramaWithSales {
  id: string
  title: string
  coverVertical: string | null
  coverHorizontal: string | null
  desp: string | null
  recommendation: string | null
  seqNum: number | null
  year: number | null
  tagList: string | null
  categoryId: string | null
  albumStatus: number | null
  originalPrice: string
  discountPrice: string
  freePreview: number | null
  currency: string
  salesCount: number
}

interface CountResult {
  count: number
}

@Injectable()
export class DouyinDramaService {
  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * 获取短剧排行榜
   * @param request 排行榜请求参数
   * @returns 分页的排行榜数据
   */
  async getDramaRanking(request: DramaRankingRequest): Promise<PaginationResponse<DramaRankingResponse>> {
    const { type = 'hot', page = 1, size = 20 } = request
    const offset = (page - 1) * size

    // 构建基础查询条件：只查询已上线的短剧
    const baseCondition = and(
      eq(dramas.isDeleted, LogicDelete.NotDeleted),
      eq(dramas.onlineStatus, 0), // 0表示已上线
    )

    let salesQuery: Promise<DramaWithSales[]>
    let totalQuery: Promise<CountResult[]>

    if (type === 'hot') {
      // 热播榜：近3个月的销量排行
      const threeMonthsAgo = new Date()
      threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3)

      // 查询近3个月的销量数据
      salesQuery = this.drizzle.db
        .select({
          id: dramas.id,
          title: dramas.title,
          coverVertical: dramas.coverVertical,
          coverHorizontal: dramas.coverHorizontal,
          desp: dramas.desp,
          recommendation: dramas.recommendation,
          seqNum: dramas.seqNum,
          year: dramas.year,
          tagList: dramas.tagList,
          categoryId: dramas.categoryId,
          albumStatus: dramas.albumStatus,
          originalPrice: dramas.originalPrice,
          discountPrice: dramas.discountPrice,
          freePreview: dramas.freePreview,
          currency: dramas.currency,
          salesCount: count(orders.id).as('salesCount'),
        })
        .from(dramas)
        .leftJoin(
          orders,
          and(
            eq(orders.dramaId, dramas.id),
            eq(orders.status, 'paid'),
            eq(orders.isDeleted, LogicDelete.NotDeleted),
            gte(orders.createdAt, threeMonthsAgo),
          ),
        )
        .where(baseCondition)
        .groupBy(dramas.id)
        .orderBy(desc(count(orders.id)))
        .limit(size)
        .offset(offset) as Promise<DramaWithSales[]>

      // 统计总数
      totalQuery = this.drizzle.db
        .select({
          count: count(),
        })
        .from(dramas)
        .where(baseCondition) as Promise<CountResult[]>
    } else {
      // 必看榜：历史销量排行
      salesQuery = this.drizzle.db
        .select({
          id: dramas.id,
          title: dramas.title,
          coverVertical: dramas.coverVertical,
          coverHorizontal: dramas.coverHorizontal,
          desp: dramas.desp,
          recommendation: dramas.recommendation,
          seqNum: dramas.seqNum,
          year: dramas.year,
          tagList: dramas.tagList,
          categoryId: dramas.categoryId,
          albumStatus: dramas.albumStatus,
          originalPrice: dramas.originalPrice,
          discountPrice: dramas.discountPrice,
          freePreview: dramas.freePreview,
          currency: dramas.currency,
          salesCount: count(orders.id).as('salesCount'),
        })
        .from(dramas)
        .leftJoin(orders, and(eq(orders.dramaId, dramas.id), eq(orders.status, 'paid'), eq(orders.isDeleted, 0)))
        .where(baseCondition)
        .groupBy(dramas.id)
        .orderBy(desc(count(orders.id)))
        .limit(size)
        .offset(offset) as Promise<DramaWithSales[]>

      // 统计总数
      totalQuery = this.drizzle.db
        .select({
          count: count(),
        })
        .from(dramas)
        .where(baseCondition) as Promise<CountResult[]>
    }

    // 执行查询
    const [salesData, totalResult] = await Promise.all([salesQuery, totalQuery])

    const total = totalResult[0]?.count ?? 0

    // 添加排名信息
    const rankedData = salesData.map((item, index) => ({
      ...item,
      rank: offset + index + 1,
    }))

    // 转换为响应格式
    const list = toResponse(DramaRankingResponse, rankedData)

    return PaginationResponse.from(list, total, page, size)
  }
}
