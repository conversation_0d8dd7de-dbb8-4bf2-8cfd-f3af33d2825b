import { Injectable, Logger } from '@nestjs/common'

import { DY_APP_ID, DY_APP_SECRET } from '@/app.config'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { systemUsers } from '@/common/drizzle/schema'
import { BizException } from '@/common/exceptions/biz.exception'
import { getDefaultAvatar, getDefaultNickname } from '@/common/utils/user.util'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { V2Jscode2sessionRequest } from '@open-dy/open_api_sdk'
import { eq } from 'drizzle-orm'
import { ClsService } from 'nestjs-cls'
import { DouyinClientProvider } from '@/modules/system/providers/douyin-client.provider'

@Injectable()
export class DouyinService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly logger: Logger,
    private readonly cls: ClsService,
    private readonly douyinClient: DouyinClientProvider,
  ) {}

  async login(anonymousCode: string, code: string) {
    this.logger.log(`小程序登录 --> code: ${code}, anonymousCode: ${anonymousCode}`)
    const session = await this.jscode2session(anonymousCode, code)

    const loginIp = this.cls.get<string>('ip')
    const loginTime = new Date()

    let user = await this.drizzle.db.query.systemUsers.findFirst({
      where: (user, { eq }) => eq(user.openId, session.openid!),
    })

    if (user) {
      await this.drizzle.db
        .update(systemUsers)
        .set({
          lastLoginAt: loginTime,
          lastLoginIp: loginIp,
        })
        .where(eq(systemUsers.id, user.id))
    }

    if (!user) {
      const [{ id: createId }] = await this.drizzle.db
        .insert(systemUsers)
        .values({
          username: session.openid!,
          password: session.openid!,
          nickname: getDefaultNickname(),
          avatar: getDefaultAvatar(),
          openId: session.openid!,
          unionId: session.unionid!,
          sessionKey: session.sessionKey!,
          anonymousOpenid: session.anonymousOpenid!,
          lastLoginAt: loginTime,
          lastLoginIp: loginIp,
        })
        .$returningId()

      this.logger.log(`新用户注册成功，用户 ID: ${createId}`)

      user = await this.drizzle.db.query.systemUsers.findFirst({
        where: (user, { eq }) => eq(user.id, createId),
      })
    }

    return user!
  }

  private async jscode2session(anonymousCode: string, code: string) {
    const params = new V2Jscode2sessionRequest({
      appid: DY_APP_ID,
      secret: DY_APP_SECRET,
      anonymousCode,
      code,
    })
    try {
      const { data } = await this.douyinClient.v2Jscode2session(params)
      return data!
    } catch (e) {
      this.logger.error(e)
      throw new BizException(ErrorCodeEnum.MINIAPP_LOGIN_FAILED)
    }
  }
}
