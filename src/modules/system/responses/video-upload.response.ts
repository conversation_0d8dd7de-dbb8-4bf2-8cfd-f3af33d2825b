import { ApiProperty } from '@nestjs/swagger'
import { Expose } from 'class-transformer'

export class VideoUploadResponse {
  @ApiProperty({ description: '上传记录ID' })
  @Expose()
  id: string

  @ApiProperty({ description: '原始视频URL' })
  @Expose()
  originalVideoUrl: string

  @ApiProperty({ description: '视频标题' })
  @Expose()
  title: string

  @ApiProperty({ description: '视频描述' })
  @Expose()
  description?: string

  @ApiProperty({ description: '抖音视频ID' })
  @Expose()
  openVideoId?: string

  @ApiProperty({ description: '上传状态', enum: [0, 1, 2] })
  @Expose()
  status: number

  @ApiProperty({ description: '失败原因' })
  @Expose()
  failureReason?: string

  @ApiProperty({ description: '创建时间' })
  @Expose()
  createdAt: Date

  @ApiProperty({ description: '更新时间' })
  @Expose()
  updatedAt: Date
}
