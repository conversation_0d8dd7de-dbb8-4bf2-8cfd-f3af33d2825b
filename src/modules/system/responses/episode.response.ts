import { ApiProperty } from '@nestjs/swagger'
import { Expose } from 'class-transformer'

export class EpisodeResponse {
  @ApiProperty({ description: '分集ID' })
  @Expose()
  id: string

  @ApiProperty({ description: '所属短剧ID' })
  @Expose()
  dramaId: string

  @ApiProperty({ description: '分集标题' })
  @Expose()
  title: string

  @ApiProperty({ description: '第几集' })
  @Expose()
  seq: number

  @ApiProperty({ description: '分集封面图片URL' })
  @Expose()
  coverUrl: string

  @ApiProperty({ description: '抖音封面图片ID' })
  @Expose()
  coverOpenPicId: string

  @ApiProperty({ description: '抖音视频ID' })
  @Expose()
  openVideoId: string

  @ApiProperty({ description: '抖音分集ID' })
  @Expose()
  douyinEpisodeId: string

  @ApiProperty({ description: '分集所属版本号' })
  @Expose()
  version: number

  @ApiProperty({ description: '分集视频地址' })
  @Expose()
  videoUrl: string

  @ApiProperty({ description: '视频方向', enum: [1, 2] })
  @Expose()
  videoOrientation: number

  @ApiProperty({ description: '视频上传状态', enum: [0, 1] })
  @Expose()
  videoUploadStatus: number

  @ApiProperty({ description: '分集简介' })
  @Expose()
  description: string

  @ApiProperty({ description: '分集时长' })
  @Expose()
  duration: string

  @ApiProperty({ description: '文件大小' })
  @Expose()
  fileSize: string

  @ApiProperty({ description: '是否免费', enum: [0, 1] })
  @Expose()
  isFree: number

  @ApiProperty({ description: '价格' })
  @Expose()
  price: string

  @ApiProperty({ description: '观看次数' })
  @Expose()
  viewCount: number

  @ApiProperty({ description: '播放次数' })
  @Expose()
  playbackCount: number

  @ApiProperty({ description: '创建时间' })
  @Expose()
  createdAt: Date

  @ApiProperty({ description: '更新时间' })
  @Expose()
  updatedAt: Date
}

export class CreateEpisodeResponse {
  @ApiProperty({ description: '分集信息' })
  @Expose()
  episode: EpisodeResponse

  @ApiProperty({ description: '短剧新版本号' })
  @Expose()
  newVersion: number

  @ApiProperty({ description: '操作消息' })
  @Expose()
  message: string
}
