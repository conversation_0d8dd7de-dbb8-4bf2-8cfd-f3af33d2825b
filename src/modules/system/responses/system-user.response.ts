import { BaseResponse } from '@/common/responses/base.response'
import { DateTimeFormat } from '@/transformers/time.transformer'
import { Exclude } from 'class-transformer'

export class SystemUserResponse extends BaseResponse {
  @Exclude()
  password: string

  @DateTimeFormat()
  lastLoginAt: string
}

export class SystemUserInfoResponse extends BaseResponse {
  @Exclude()
  password: string

  // 创作者是否通过了创作者信息审核
  hasCreatorApproval: boolean
}
