import { IsNotEmpty, IsOptional, IsString, <PERSON>Length } from 'class-validator'

export class SystemConfigCreateRequest {
  @IsNotEmpty({ message: '配置键不能为空' })
  @IsString()
  @MaxLength(50)
  configKey: string

  @IsNotEmpty({ message: '配置值不能为空' })
  @IsString()
  @MaxLength(255)
  configValue: string

  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string
}

export class SystemConfigUpdateRequest {
  @IsOptional()
  @IsString()
  @MaxLength(50)
  configKey?: string

  @IsOptional()
  @IsString()
  @MaxLength(255)
  configValue?: string

  @IsOptional()
  @IsString()
  @MaxLength(255)
  description?: string
}
