import { OmitType } from '@nestjs/mapped-types'
import { IsIn, IsOptional, IsString, MaxLength } from 'class-validator'

export class SystemGenresCreateRequest {
  @IsString()
  @MaxLength(50)
  name: string

  @IsIn([0, 1])
  @IsOptional()
  status: number
}

export class SystemGenresUpdateRequest extends SystemGenresCreateRequest {}

export class SystemGenresFilterRequest extends OmitType(SystemGenresCreateRequest, ['name']) {}
