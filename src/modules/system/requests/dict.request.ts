import { IsInt, <PERSON>NotEmpty, <PERSON>Optional, IsString, MaxLength } from 'class-validator'

export class SystemDictCreateRequest {
  @IsNotEmpty({ message: '字典类型不能为空' })
  @IsString()
  @MaxLength(50)
  type: string

  @IsNotEmpty({ message: '字典键不能为空' })
  @IsString()
  @MaxLength(50)
  key: string

  @IsNotEmpty({ message: '字典值不能为空' })
  @IsString()
  @MaxLength(255)
  value: string

  @IsOptional()
  @IsInt()
  order?: number

  @IsOptional()
  @IsInt()
  status?: number

  @IsOptional()
  @IsString()
  remark?: string
}

export class SystemDictUpdateRequest {
  @IsOptional()
  @IsString()
  @MaxLength(50)
  type?: string

  @IsOptional()
  @IsString()
  @MaxLength(50)
  key?: string

  @IsOptional()
  @IsString()
  @MaxLength(255)
  value?: string

  @IsOptional()
  @IsInt()
  order?: number

  @IsOptional()
  @IsInt()
  status?: number

  @IsOptional()
  @IsString()
  remark?: string
}

export class SystemDictFilterRequest {
  @IsOptional()
  @IsString()
  type?: string

  @IsOptional()
  @IsString()
  key?: string

  @IsOptional()
  @IsInt()
  status?: number
}
