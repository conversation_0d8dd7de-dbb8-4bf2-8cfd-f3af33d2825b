import { getDefaultAvatar, getDefaultNickname, getDefaultPassword } from '@/common/utils/user.util'
import { RoleEnum } from '@/modules/auth/interfaces/jwt-payload.interface'
import { OmitType } from '@nestjs/mapped-types'
import { IsEmail, IsEmpty, IsEnum, IsInt, IsNotEmpty, IsOptional, IsPhoneNumber } from 'class-validator'

export class SystemUserLoginRequest {
  phonenumber: string
  code: string
}

export class SystemUserCreateRequest {
  @IsNotEmpty({ message: '用户名不能为空' })
  username: string

  @IsOptional()
  nickname: string = getDefaultNickname()

  @IsOptional()
  password?: string = getDefaultPassword()

  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  email: string

  @IsPhoneNumber('CN')
  @IsNotEmpty({ message: '手机号不能为空' })
  phone: string

  @IsOptional()
  avatar: string = getDefaultAvatar()

  @IsOptional()
  @IsInt()
  @IsEnum(RoleEnum)
  role: number

  @IsOptional()
  status: number
}

export class SystemUserUpdateRequest extends OmitType(SystemUserCreateRequest, [
  'username',
  'phone',
  'email',
] as const) {
  @IsEmpty({ message: '用户名不能修改' })
  username: string

  @IsOptional()
  @IsPhoneNumber('CN')
  phone?: string

  @IsOptional()
  @IsEmail({}, { message: '邮箱格式不正确' })
  email?: string
}

export class SystemUserFilterRequest {
  @IsOptional()
  @IsInt()
  @IsEnum(RoleEnum)
  role?: number

  @IsOptional()
  phone?: string

  @IsOptional()
  username?: string
}
