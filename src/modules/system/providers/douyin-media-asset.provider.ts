import { DrizzleService } from '@/common/drizzle/database.provider'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { Injectable, Logger } from '@nestjs/common'
import { AxiosInstance, AxiosResponse } from 'axios'
import instance from './axios.instance'
import { DouyinClientProvider } from './douyin-client.provider'
import {
  API_ENDPOINTS,
  DeleteVideoParams,
  DescribeVodPlayedStaticDataParams,
  DescribeVodPlayedTopDataParams,
  GetUploadJobInfoParams,
  GetVideoByVidParams,
  GetWorkflowExecutionParams,
  StartWorkflowParams,
  UploadVideoByUrlsParams,
  VideoListParams,
} from './interfaces/douyin-media-asset.interface'
import { DouyinResponse } from './interfaces/douyin-drama-management.interface'

/**
 * 抖音短剧媒资管理的 API 接口
 * https://developer.open-douyin.com/docs/resource/zh-CN/developer/tools/cloud/guide/industry-solutions/short-drama-media-api
 */
@Injectable()
export class DouyinMediaAssetProvider {
  private readonly instance: AxiosInstance
  constructor(
    private readonly douyinClient: DouyinClientProvider,
    private readonly drizzle: DrizzleService,
    private readonly logger: Logger,
  ) {
    this.instance = instance
  }

  /**
   * 获取视频列表
   */
  async getVideoList(params: VideoListParams = {}) {
    const { page_size = 10, page_number = 0, env_id = '1', business_status = '99', video_name = '' } = params

    const response = await this.makeRequest(API_ENDPOINTS.GET_VIDEO_LIST, {
      page_size,
      page_number,
      env_id,
      business_status,
      video_name,
    })

    this.logger.log(`Successfully fetched video list with params: ${JSON.stringify(params)}`)
    return response.data
  }

  /**
   * 删除视频
   */
  async deleteVideo(params: DeleteVideoParams) {
    const { env_id, vid } = params

    const response = await this.makeRequest(API_ENDPOINTS.DELETE_VIDEO, { env_id, vid })

    this.logger.log(`Successfully deleted video: ${vid}`)
    return response.data
  }

  /**
   * 根据视频ID获取视频信息
   */
  async getVideoByVid(params: GetVideoByVidParams) {
    const { vid } = params

    const response = await this.makeRequest(API_ENDPOINTS.GET_VIDEO_BY_VID, { vid })

    this.logger.log(`Successfully fetched video info: ${vid}`)
    return response.data
  }

  /**
   * 通过URL上传视频
   */
  async uploadVideoByUrls(params: UploadVideoByUrlsParams) {
    const { url_sets } = params

    const response = await this.makeRequest(API_ENDPOINTS.UPLOAD_VIDEO_BY_URLS, { url_sets })

    this.logger.log(`Successfully uploaded video by URLs: ${url_sets.map((set) => set.file_name).join(', ')}`)
    return response.data
  }

  /**
   * 获取上传任务信息
   */
  async getUploadJobInfo(params: GetUploadJobInfoParams) {
    const { job_ids } = params

    const response = await this.makeRequest(API_ENDPOINTS.GET_UPLOAD_JOB_INFO, { job_ids })

    this.logger.log(`Successfully fetched upload job info: ${job_ids.join(', ')}`)
    return response.data
  }

  /**
   * 启动工作流
   */
  async startWorkflow(params: StartWorkflowParams) {
    const { vid, trans_code_type } = params

    const response = await this.makeRequest(API_ENDPOINTS.START_WORKFLOW, { vid, trans_code_type })

    this.logger.log(`Successfully started workflow for video: ${vid}`)
    return response.data
  }

  /**
   * 获取工作流执行状态
   */
  async getWorkflowExecution(params: GetWorkflowExecutionParams) {
    const { run_id } = params

    const response = await this.makeRequest(API_ENDPOINTS.GET_WORKFLOW_EXECUTION, { run_id })

    this.logger.log(`Successfully fetched workflow execution: ${run_id}`)
    return response.data
  }

  /**
   * 获取视频播放量排行数据
   */
  async describeVodPlayedTopData(params: DescribeVodPlayedTopDataParams) {
    const { env_id, start_time, end_time, order_type } = params

    const response = await this.makeRequest(API_ENDPOINTS.DESCRIBE_VOD_PLAYED_TOP_DATA, {
      env_id,
      start_time,
      end_time,
      order_type,
    })

    this.logger.log(`Successfully fetched vod played top data for env: ${env_id}`)
    return response.data
  }

  /**
   * 获取视频播放量统计数据
   */
  async describeVodPlayedStaticData(params: DescribeVodPlayedStaticDataParams) {
    const { env_id, start_time, end_time, order_type, vid_list, app_id_list } = params

    const response = await this.makeRequest(API_ENDPOINTS.DESCRIBE_VOD_PLAYED_STATIC_DATA, {
      env_id,
      start_time,
      end_time,
      order_type,
      vid_list,
      app_id_list,
    })

    this.logger.log(`Successfully fetched vod played static data for env: ${env_id}`)
    return response.data
  }

  /**
   * 统一的HTTP请求方法
   */
  private async makeRequest(endpoint: string, data: any): Promise<AxiosResponse> {
    const accessToken = await this.getClientToken()
    const response = await this.instance.post<DouyinResponse<any>>(endpoint, data, {
      headers: {
        'Content-Type': 'application/json',
        'access-token': accessToken,
      },
    })

    if (response.data?.err_no && response.data.err_no !== 0) {
      this.logger.error(`${endpoint} 请求失败: ${JSON.stringify(data)}`)
      throw new BizException(ErrorCodeEnum.DOUYIN_API_ERROR)
    }

    return response
  }

  /**
   * 获取客户端访问令牌
   */
  private async getClientToken(): Promise<string> {
    const tokenInfo = await this.douyinClient.getOAuthClientToken()
    if (!tokenInfo) {
      throw new BizException(ErrorCodeEnum.DOUYIN_CLIENT_TOKEN_FAILED)
    }
    return tokenInfo.access_token
  }
}
