import axios from 'axios'
import * as JSONBigNumber from 'json-bignumber'

const DOUYIN_API_BASE_URL = 'https://open.douyin.com/api'

function transformResponse(data: unknown): unknown {
  try {
    return JSONBigNumber.parse(data as string)
  } catch (err) {
    console.error(err)
    return data
  }
}

const instance = axios.create({
  baseURL: DOUYIN_API_BASE_URL,
  timeout: 30000,
  transformResponse: [transformResponse],
  headers: {
    'Content-Type': 'application/json',
  },
})

// // 添加请求拦截器
// instance.interceptors.request.use(
//   (config: InternalAxiosRequestConfig) => {
//     console.log('请求配置Request Config:', {
//       url: config.url,
//       method: config.method,
//       baseURL: config.baseURL,
//       headers: config.headers,
//       data: config.data as unknown,
//       params: config.params as unknown,
//     })
//     return config
//   },
//   (error: AxiosError) => {
//     console.error('错误的Request Error:', error)
//     return Promise.reject(new Error(error.message))
//   },
// )
//
// // 添加响应拦截器
// instance.interceptors.response.use(
//   (response: AxiosResponse) => {
//     console.log('正确的Response:', {
//       status: response.status,
//       statusText: response.statusText,
//       headers: response.headers,
//       data: response.data as unknown,
//     })
//     return response
//   },
//   (error: AxiosError) => {
//     const errorMessage = (error.response?.data as string) || error.message
//     console.error('错误的Response Error:', errorMessage)
//     return Promise.reject(new Error(errorMessage))
//   },
// )

export default instance
