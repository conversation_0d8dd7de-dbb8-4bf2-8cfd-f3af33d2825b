/**
 * 抖音云媒资管理的 API 接口类型定义
 * https://developer.open-douyin.com/docs/resource/zh-CN/developer/tools/cloud/guide/industry-solutions/short-drama-media-api
 */

// API 端点常量
export const API_ENDPOINTS = {
  GET_VIDEO_LIST: '/dyc_voc/get_video_list',
  DELETE_VIDEO: '/dyc_voc/delete_video',
  GET_VIDEO_BY_VID: '/dyc_voc/get_video_by_vid',
  UPLOAD_VIDEO_BY_URLS: '/dyc_voc/upload_video_by_urls',
  GET_UPLOAD_JOB_INFO: '/dyc_voc/get_upload_job_info',
  START_WORKFLOW: '/dyc_voc/start_work_flow',
  GET_WORKFLOW_EXECUTION: '/dyc_voc/get_work_flow_exection',
  DESCRIBE_VOD_PLAYED_TOP_DATA: '/dyc_voc/describe_vod_played_top_data',
  DESCRIBE_VOD_PLAYED_STATIC_DATA: '/dyc_voc/describe_vod_played_static_data',
} as const

// 请求参数类型定义
export interface VideoListParams {
  page_size?: number
  page_number?: number
  env_id?: string
  business_status?: string
  video_name?: string
}

export interface DeleteVideoParams {
  env_id: string
  vid: string
}

export interface GetVideoByVidParams {
  vid: string
}

export interface UrlSet {
  source_url: string
  file_name: string
}

export interface UploadVideoByUrlsParams {
  url_sets: UrlSet[]
}

export interface GetUploadJobInfoParams {
  job_ids: string[]
}

export interface StartWorkflowParams {
  vid: string
  trans_code_type: string
}

export interface GetWorkflowExecutionParams {
  run_id: string
}

export interface DescribeVodPlayedTopDataParams {
  env_id: string
  start_time: string
  end_time: string
  order_type: string
}

export interface DescribeVodPlayedStaticDataParams {
  env_id: string
  start_time: string
  end_time: string
  order_type: string
  vid_list: string[]
  app_id_list: string[]
}

// 响应数据类型定义
export interface DouyinApiResponse<T = any> {
  data: T
  message?: string
  code?: number
}

export interface VideoInfo {
  vid: string
  video_name: string
  video_url: string
  duration: number
  size: number
  status: string
  created_at: string
  updated_at: string
}

export interface VideoListResponse {
  total: number
  videos: VideoInfo[]
}

export interface UploadJobInfo {
  job_id: string
  status: string
  progress: number
  created_at: string
  updated_at: string
}

export interface WorkflowExecutionInfo {
  run_id: string
  status: string
  progress: number
  created_at: string
  updated_at: string
}

export interface VodPlayedData {
  vid: string
  play_count: number
  play_duration: number
  date: string
}

export interface VodPlayedTopDataResponse {
  total: number
  data: VodPlayedData[]
}

export interface VodPlayedStaticDataResponse {
  total: number
  data: VodPlayedData[]
}
