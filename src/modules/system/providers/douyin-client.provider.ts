import { DY_APP_ID, DY_APP_SECRET } from '@/app.config'
import { CacheService } from '@/common/redis/cache.service'
import { RedisKeys } from '@/constants/cache.constants'
import { Injectable, Logger } from '@nestjs/common'
import Client, { OauthClientTokenRequest, OauthClientTokenResponseData } from '@open-dy/open_api_sdk'

@Injectable()
export class DouyinClientProvider extends Client {
  constructor(
    private readonly logger: Logger,
    private readonly cacheService: CacheService,
  ) {
    super({
      clientKey: DY_APP_ID,
      clientSecret: DY_APP_SECRET,
    })
  }

  async getOAuthClientToken() {
    const cacheResult = await this.cacheService.get<OauthClientTokenResponseData>(RedisKeys.DY_OAUTH_CLIENT_TOKEN_KEY)
    if (cacheResult) {
      return cacheResult
    }

    const params = new OauthClientTokenRequest({
      clientKey: DY_APP_ID,
      clientSecret: DY_APP_SECRET,
      grantType: 'client_credential',
    })

    const { data } = await this.oauthClientToken(params)
    await this.cacheService.set(RedisKeys.DY_OAUTH_CLIENT_TOKEN_KEY, data, Number(data?.expiresIn ?? 0) * 1000)
    this.logger.log(`get douyin oauth token success, token: ${data?.accessToken}`)
    return data
  }
}
