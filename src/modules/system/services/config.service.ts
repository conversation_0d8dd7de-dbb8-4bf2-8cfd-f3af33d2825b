import { Injectable } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { systemConfigs } from '@/common/drizzle/schema/system-configs'
import { eq } from 'drizzle-orm'
import { toResponse } from '@/common/utils/transform.util'
import { SystemConfigCreateRequest, SystemConfigUpdateRequest } from '../requests/config.request'
import { SystemConfigResponse } from '../responses/config.response'
import { getUserId } from '@/context/user.context'

@Injectable()
export class SystemConfigService {
  constructor(private readonly drizzle: DrizzleService) {}

  async findAll() {
    const data = await this.drizzle.db.query.systemConfigs.findMany({
      orderBy: (configs, { desc }) => desc(configs.updatedAt),
    })
    return toResponse(SystemConfigResponse, data)
  }

  async findOne(id: string) {
    const config = await this.drizzle.db.query.systemConfigs.findFirst({
      where: (configs, { eq }) => eq(configs.id, id),
    })
    return toResponse(SystemConfigResponse, config)
  }

  async create(dto: SystemConfigCreateRequest) {
    const [{ id }] = await this.drizzle.db.insert(systemConfigs).values(dto).$returningId()
    return id
  }

  async update(id: string, dto: SystemConfigUpdateRequest) {
    const userId = getUserId()

    const [{ affectedRows }] = await this.drizzle.db
      .update(systemConfigs)
      .set({ ...dto, updatedBy: userId })
      .where(eq(systemConfigs.id, id))
    return affectedRows > 0
  }

  async delete(id: string) {
    const [{ affectedRows }] = await this.drizzle.db.delete(systemConfigs).where(eq(systemConfigs.id, id))
    return affectedRows > 0
  }
}
