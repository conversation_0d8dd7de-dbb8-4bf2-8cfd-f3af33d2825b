import { Injectable } from '@nestjs/common'
import { DrizzleService, DrizzleDB } from '@/common/drizzle/database.provider'
import { comments, dramas, episodes, systemUsers } from '@/common/drizzle/schema'
import { eq, and, like, SQL, sql, inArray } from 'drizzle-orm'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { Role } from '@/modules/auth/interfaces/jwt-payload.interface'

import { toResponse } from '@/common/utils/transform.util'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { LogicDelete } from '@/constants/system.constants'
import {
  SystemCommentCreateRequest,
  SystemCommentUpdateRequest,
  SystemCommentFilterRequest,
} from '../requests/comment.request'
import { SystemCommentResponse } from '../responses/comment.response'

@Injectable()
export class SystemCommentService {
  constructor(private readonly drizzle: DrizzleService) {}

  /**
   * 获取创作者拥有的短剧ID列表
   */
  private async getCreatorDramaIds(userId: string): Promise<string[]> {
    const creatorDramas = await this.drizzle.db.query.dramas.findMany({
      where: eq(dramas.authorId, userId),
      columns: {
        id: true,
      },
    })
    return creatorDramas.map((drama) => drama.id)
  }

  /**
   * 验证用户是否存在
   */
  private async validateUser(userId: string): Promise<void> {
    const user = await this.drizzle.db.query.systemUsers.findFirst({
      where: eq(systemUsers.id, userId),
      columns: { id: true },
    })
    if (!user) {
      throw new BizException(ErrorCodeEnum.USER_NOT_FOUND)
    }
  }

  /**
   * 验证短剧和分集是否存在
   */
  private async validateDramaAndEpisode(dramaId: string, episodeId: string): Promise<void> {
    // 验证短剧是否存在
    const drama = await this.drizzle.db.query.dramas.findFirst({
      where: eq(dramas.id, dramaId),
      columns: { id: true },
    })
    if (!drama) {
      throw new BizException(ErrorCodeEnum.DRAMA_NOT_FOUND)
    }

    // 验证分集是否存在且属于该短剧
    const episode = await this.drizzle.db.query.episodes.findFirst({
      where: and(eq(episodes.id, episodeId), eq(episodes.dramaId, dramaId)),
      columns: { id: true },
    })
    if (!episode) {
      throw new BizException(ErrorCodeEnum.EPISODE_NOT_FOUND)
    }
  }

  /**
   * 验证父级评论是否存在
   */
  private async validateParentComment(parentId: string): Promise<void> {
    const parentComment = await this.drizzle.db.query.comments.findFirst({
      where: and(eq(comments.id, parentId), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      columns: { id: true },
    })
    if (!parentComment) {
      throw new BizException(ErrorCodeEnum.COMMENT_NOT_FOUND)
    }
  }

  /**
   * 完整的创建评论数据验证
   */
  private async validateCreateCommentData(dto: SystemCommentCreateRequest): Promise<void> {
    // 验证用户是否存在
    await this.validateUser(dto.userId)

    // 验证短剧和分集是否存在
    await this.validateDramaAndEpisode(dto.dramaId, dto.episodeId)

    // 验证父级评论是否存在（如果有）
    if (dto.parentId) {
      await this.validateParentComment(dto.parentId)
    }

    // 验证被回复用户是否存在（如果有）
    if (dto.replyToUserId) {
      await this.validateUser(dto.replyToUserId)
    }
  }

  /**
   * 检查创作者是否有权限操作该短剧下的评论
   */
  private async checkCreatorPermission(userId: string, userRole: number, dramaId: string): Promise<void> {
    // 超级管理员不受限制
    if (userRole === Role.ADMIN) {
      return
    }

    // 创作者只能操作自己的短剧
    if (userRole === Role.CREATOR) {
      const creatorDramaIds = await this.getCreatorDramaIds(userId)
      if (!creatorDramaIds.includes(dramaId)) {
        throw new BizException(ErrorCodeEnum.FORBIDDEN)
      }
    }
  }

  async findAll(
    pagination: PaginationRequest,
    filter: SystemCommentFilterRequest,
    currentUserId?: string,
    currentUserRole?: number,
  ) {
    const buildCondition = async (): Promise<SQL | undefined> => {
      const wheres: SQL[] = [eq(comments.isDeleted, LogicDelete.NotDeleted)]

      // 创作者权限限制：只能查看自己短剧下的评论
      if (currentUserRole === Role.CREATOR && currentUserId) {
        const creatorDramaIds = await this.getCreatorDramaIds(currentUserId)
        if (creatorDramaIds.length > 0) {
          wheres.push(inArray(comments.dramaId, creatorDramaIds))
        } else {
          // 如果创作者没有任何短剧，返回空结果
          wheres.push(eq(comments.id, 'impossible-id'))
        }
      }

      if (filter.userId) wheres.push(eq(comments.userId, filter.userId))
      if (filter.dramaId) wheres.push(eq(comments.dramaId, filter.dramaId))
      if (filter.episodeId) wheres.push(eq(comments.episodeId, filter.episodeId))
      if (filter.parentId) wheres.push(eq(comments.parentId, filter.parentId))
      if (filter.status !== undefined) wheres.push(eq(comments.status, filter.status))
      if (filter.isFeatured !== undefined) wheres.push(eq(comments.isFeatured, filter.isFeatured))
      if (filter.content) wheres.push(like(comments.content, `%${filter.content}%`))

      return wheres.length ? and(...wheres) : undefined
    }

    // 使用关联查询来获取用户、短剧和分集信息
    const condition = await buildCondition()
    const offset = (pagination.page - 1) * pagination.size

    const data = await this.drizzle.db.query.comments.findMany({
      where: condition || undefined,
      limit: Number(pagination.size),
      offset: Number(offset),
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
        drama: {
          columns: {
            id: true,
            title: true,
          },
        },
        episode: {
          columns: {
            id: true,
            title: true,
            seq: true,
          },
        },
        replyToUser: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
      orderBy: (comments, { desc }) => [desc(comments.createdAt)],
    })

    // 获取总数
    const countResult = await this.drizzle.db
      .select({ count: sql`count(*)`.mapWith(Number) })
      .from(comments)
      .where(condition || undefined)

    const total = countResult[0]?.count ?? 0

    const list = toResponse(SystemCommentResponse, data)
    return PaginationResponse.fromPaginationRequest(list, total, pagination)
  }

  async findOne(id: string) {
    const comment = await this.drizzle.db.query.comments.findFirst({
      where: (comments, { eq, and }) => and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
        drama: {
          columns: {
            id: true,
            title: true,
          },
        },
        episode: {
          columns: {
            id: true,
            title: true,
            seq: true,
          },
        },
        replyToUser: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
    })
    return comment ? toResponse(SystemCommentResponse, comment) : null
  }

  async create(dto: SystemCommentCreateRequest, currentUserId?: string, currentUserRole?: number) {
    // 完整的数据验证
    await this.validateCreateCommentData(dto)

    // 检查创作者权限
    if (currentUserId && currentUserRole !== undefined) {
      await this.checkCreatorPermission(currentUserId, currentUserRole, dto.dramaId)
    }

    const [{ id }] = await this.drizzle.db.insert(comments).values(dto).$returningId()
    return id
  }

  async update(id: string, dto: SystemCommentUpdateRequest, currentUserId?: string, currentUserRole?: number) {
    // 先获取评论信息，检查权限
    const comment = await this.drizzle.db.query.comments.findFirst({
      where: and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      columns: {
        id: true,
        dramaId: true,
      },
    })

    if (!comment) {
      throw new BizException(ErrorCodeEnum.COMMENT_NOT_FOUND)
    }

    // 检查创作者权限
    if (currentUserId && currentUserRole !== undefined) {
      await this.checkCreatorPermission(currentUserId, currentUserRole, comment.dramaId)
    }

    const [{ affectedRows }] = await this.drizzle.db
      .update(comments)
      .set(dto)
      .where(and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)))
    return affectedRows > 0
  }

  async delete(id: string, currentUserId?: string, currentUserRole?: number) {
    // 先获取评论信息，检查权限
    const comment = await this.drizzle.db.query.comments.findFirst({
      where: and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      columns: {
        id: true,
        dramaId: true,
      },
    })

    if (!comment) {
      throw new BizException(ErrorCodeEnum.COMMENT_NOT_FOUND)
    }

    // 检查创作者权限
    if (currentUserId && currentUserRole !== undefined) {
      await this.checkCreatorPermission(currentUserId, currentUserRole, comment.dramaId)
    }

    return await this.drizzle.db.transaction(async (tx) => {
      // 级联删除：先删除所有子评论
      await this.cascadeDeleteRepliesWithTx(tx, id)

      // 删除主评论
      const [{ affectedRows }] = await tx
        .update(comments)
        .set({ isDeleted: LogicDelete.Deleted })
        .where(eq(comments.id, id))
      return affectedRows > 0
    })
  }

  /**
   * 级联删除所有子评论（管理员端）
   */
  private async cascadeDeleteRepliesWithTx(
    tx: Parameters<Parameters<DrizzleDB['transaction']>[0]>[0],
    parentId: string,
  ): Promise<void> {
    // 查找所有直接子评论
    const childComments = await tx.query.comments.findMany({
      where: and(eq(comments.parentId, parentId), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      columns: {
        id: true,
      },
    })

    if (childComments.length === 0) {
      return
    }

    // 递归删除每个子评论的子评论
    for (const child of childComments) {
      await this.cascadeDeleteRepliesWithTx(tx, child.id)
    }

    // 删除所有直接子评论
    const childIds = childComments.map((c: { id: string }) => c.id)
    await tx.update(comments).set({ isDeleted: LogicDelete.Deleted }).where(inArray(comments.id, childIds))
  }

  async setFeatured(id: string, isFeatured: number, currentUserId?: string, currentUserRole?: number) {
    // 先获取评论信息，检查权限
    const comment = await this.drizzle.db.query.comments.findFirst({
      where: and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      columns: {
        id: true,
        dramaId: true,
      },
    })

    if (!comment) {
      throw new BizException(ErrorCodeEnum.COMMENT_NOT_FOUND)
    }

    // 检查创作者权限
    if (currentUserId && currentUserRole !== undefined) {
      await this.checkCreatorPermission(currentUserId, currentUserRole, comment.dramaId)
    }

    const [{ affectedRows }] = await this.drizzle.db
      .update(comments)
      .set({ isFeatured })
      .where(and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)))
    return affectedRows > 0
  }
}
