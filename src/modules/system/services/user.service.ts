import { DrizzleService } from '@/common/drizzle/database.provider'
import { systemUsers } from '@/common/drizzle/schema'
import { UserModel } from '@/common/drizzle/schema/system-users'
import { BizException } from '@/common/exceptions/biz.exception'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { isUndefined } from '@/common/utils/misc.util'
import { pageQuery } from '@/common/utils/page-query.util'
import { toResponse } from '@/common/utils/transform.util'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { LogicDelete } from '@/constants/system.constants'
import { Injectable } from '@nestjs/common'
import { and, eq, like, not, SQLWrapper } from 'drizzle-orm'
import {
  SystemUserCreateRequest,
  SystemUserFilterRequest,
  SystemUserUpdateRequest,
} from '../requests/system-user.request'
import { SystemUserInfoResponse, SystemUserResponse } from '../responses/system-user.response'

@Injectable()
export class SystemUserService {
  constructor(private readonly drizzle: DrizzleService) {}

  // 用户登录
  async userLogin(phonenumber: string) {
    const user = await this.drizzle.db.query.systemUsers.findFirst({
      where: (systemUsers, { eq, and }) =>
        and(eq(systemUsers.phone, phonenumber), eq(systemUsers.isDeleted, LogicDelete.NotDeleted)),
    })

    if (!user) {
      throw new BizException(ErrorCodeEnum.ADMIN_USER_NOT_FOUND)
    }

    return user
  }

  // 获取所有用户
  async findAll(paginationRequest: PaginationRequest, filter?: SystemUserFilterRequest) {
    const buildCondition = (filter?: SystemUserFilterRequest) => {
      const wheres: SQLWrapper[] = [eq(systemUsers.isDeleted, LogicDelete.NotDeleted)]

      if (filter) {
        if (!isUndefined(filter.role)) {
          wheres.push(eq(systemUsers.role, filter.role))
        }
        if (filter.phone && filter.phone.trim() !== '') {
          wheres.push(like(systemUsers.phone, `%${filter.phone}%`))
        }
        if (filter.username && filter.username.trim() !== '') {
          wheres.push(like(systemUsers.username, `%${filter.username}%`))
        }
      }
      return and(...wheres)
    }

    const { getData } = pageQuery(this.drizzle.db, systemUsers, paginationRequest, {
      condition: buildCondition(filter),
    })

    const { data, total } = await getData()

    const list = toResponse(SystemUserResponse, data)

    return PaginationResponse.fromPaginationRequest(list, total, paginationRequest)
  }

  // 通过 ID 获取用户
  async findOne(id: string) {
    const dbUser = await this.drizzle.db.query.systemUsers.findFirst({
      where: (systemUsers, { eq, and }) =>
        and(eq(systemUsers.id, id), eq(systemUsers.isDeleted, LogicDelete.NotDeleted)),
    })
    if (!dbUser) {
      throw new BizException(ErrorCodeEnum.USER_NOT_FOUND)
    }

    return toResponse(SystemUserResponse, dbUser)
  }

  // 创建用户
  async create(systemUserCreateRequest: SystemUserCreateRequest) {
    const [{ id }] = await this.drizzle.db.insert(systemUsers).values(systemUserCreateRequest).$returningId()
    return id
  }

  // 通过 ID 更新用户
  async update(id: string, data: SystemUserUpdateRequest) {
    // 检查手机号是否已被其他用户使用
    if (data.phone) {
      const phone = data.phone
      const existingUserWithPhone = await this.drizzle.db.query.systemUsers.findFirst({
        where: (systemUsers, { eq, and }) =>
          and(
            eq(systemUsers.phone, phone),
            not(eq(systemUsers.id, id)),
            eq(systemUsers.isDeleted, LogicDelete.NotDeleted),
          ),
      })
      if (existingUserWithPhone) {
        throw new BizException(ErrorCodeEnum.PHONE_ALREADY_EXISTS)
      }
    }

    // 检查邮箱是否已被其他用户使用
    if (data.email) {
      const email = data.email
      const existingUserWithEmail = await this.drizzle.db.query.systemUsers.findFirst({
        where: (systemUsers, { eq, and }) =>
          and(
            eq(systemUsers.email, email),
            not(eq(systemUsers.id, id)),
            eq(systemUsers.isDeleted, LogicDelete.NotDeleted),
          ),
      })
      if (existingUserWithEmail) {
        throw new BizException(ErrorCodeEnum.EMAIL_ALREADY_EXISTS)
      }
    }

    const [{ affectedRows }] = await this.drizzle.db
      .update(systemUsers)
      .set(data)
      .where(and(eq(systemUsers.id, id), eq(systemUsers.isDeleted, LogicDelete.NotDeleted)))
    return affectedRows > 0
  }

  // 通过 ID 删除用户
  async delete(id: string) {
    const [{ affectedRows }] = await this.drizzle.db
      .update(systemUsers)
      .set({ isDeleted: LogicDelete.Deleted })
      .where(and(eq(systemUsers.id, id), eq(systemUsers.isDeleted, LogicDelete.NotDeleted)))

    return affectedRows > 0
  }

  // 获取用户信息并包含创作者审核状态
  getUserInfoWithCreatorStatus(user: UserModel) {
    return toResponse(SystemUserInfoResponse, user)
  }
}
