import { Injectable, Logger } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { LockService } from '@/common/redis/lock.service'
import { douyinAuditRecords } from '@/common/drizzle/schema'
import { and, eq } from 'drizzle-orm'
import {
  DouyinCallbackTypeEnum,
  DouyinCallbackData,
  CreateAuditRecordRequest,
  AuditRecordResponse,
  AlbumAuditCallbackData,
  EpisodeAuditCallbackData,
  UploadVideoCallbackData,
  PostReviewCallbackData,
  DouyinCallbackType,
} from '../interfaces/douyin-callback.interface'

@Injectable()
export class DouyinAuditRecordService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly logger: Logger,
    private readonly lockService: LockService,
  ) {}

  /**
   * 生成分布式锁键
   * @param callbackType 回调类型
   * @param callbackData 回调数据
   * @returns 锁键
   */
  private generateLockKey(callbackType: DouyinCallbackType, callbackData: DouyinCallbackData): string {
    let uniqueKey: string

    switch (callbackType) {
      case DouyinCallbackTypeEnum.ALBUM_AUDIT: {
        const data = callbackData as AlbumAuditCallbackData
        // {albumId}:{version}:{auditStatus}
        uniqueKey = `${data.album_id}:${data.version}:${data.audit_status}`
        break
      }

      case DouyinCallbackTypeEnum.EPISODE_AUDIT: {
        const data = callbackData as EpisodeAuditCallbackData
        // {episodeId}:{version}:{auditStatus}
        uniqueKey = `${data.episode_id}:${data.version}:${data.audit_status}`
        break
      }

      case DouyinCallbackTypeEnum.UPLOAD_VIDEO: {
        const data = callbackData as UploadVideoCallbackData
        // {openVideoId}:{success}
        uniqueKey = `${data.open_video_id}:${data.success}`
        break
      }

      case DouyinCallbackTypeEnum.POST_REVIEW: {
        const data = callbackData as PostReviewCallbackData
        // {reviewId}:{reviewResult}
        uniqueKey = `${data.review_id}:${data.review_result}`
        break
      }

      default:
        // 对于未知类型，使用原始数据的hash作为唯一键
        uniqueKey = Buffer.from(JSON.stringify(callbackData)).toString('base64')
    }

    return `douyin:audit:lock:${callbackType}:${uniqueKey}`
  }

  /**
   * 创建审核记录（幂等性处理）
   * @param request 创建请求
   * @returns 审核记录响应
   */
  async createRecord(request: CreateAuditRecordRequest): Promise<AuditRecordResponse> {
    const { callbackType, callbackData, version } = request

    this.logger.log(`创建审核记录: type=${callbackType}, version=${version}`)

    // 生成分布式锁键 TODO 生成锁键规则看是否还需要重写
    const lockKey = this.generateLockKey(callbackType, callbackData)

    // 获取分布式锁（TTL: 5秒）
    const lock = await this.lockService.acquireLock(lockKey, 5000)

    try {
      this.logger.log(`获取分布式锁成功: ${lockKey}`)

      // 在锁保护下执行幂等逻辑
      // 1. 先查询是否已存在相同记录
      const existingRecord = await this.findExistingRecord(callbackType, callbackData)

      if (existingRecord) {
        this.logger.log(`审核记录已存在，返回现有记录: type=${callbackType}, id=${existingRecord.id}`)
        return {
          id: existingRecord.id,
          isNew: false,
          record: existingRecord,
        }
      }

      // 2. 不存在则创建新记录
      // 构建记录数据
      const recordData = this.buildRecordData(callbackType, callbackData, version)

      // 插入新记录
      const [insertResult] = await this.drizzle.db.insert(douyinAuditRecords).values(recordData).$returningId()

      this.logger.log(`审核记录创建成功: id=${insertResult.id}`)

      // 查询完整记录
      const record = await this.drizzle.db.query.douyinAuditRecords.findFirst({
        where: eq(douyinAuditRecords.id, insertResult.id),
      })

      return {
        id: insertResult.id,
        isNew: true,
        record,
      }
    } finally {
      // 确保在所有情况下都释放锁
      await lock.release()
      this.logger.log(`释放分布式锁: ${lockKey}`)
    }
  }

  /**
   * 构建记录数据
   */
  private buildRecordData(callbackType: DouyinCallbackType, callbackData: DouyinCallbackData, version: string) {
    const baseData = {
      callbackType,
      rawCallbackData: callbackData,
      callbackTime: new Date(),
      processed: false,
    }

    switch (callbackType) {
      case DouyinCallbackTypeEnum.ALBUM_AUDIT: {
        const data = callbackData as AlbumAuditCallbackData
        return {
          ...baseData,
          albumId: data.album_id.toString(),
          version: data.version,
          auditStatus: data.audit_status,
          scopeList: data.scope_list,
          auditMsg: data.audit_msg,
        }
      }

      case DouyinCallbackTypeEnum.EPISODE_AUDIT: {
        const data = callbackData as EpisodeAuditCallbackData
        return {
          ...baseData,
          albumId: data.album_id.toString(),
          episodeId: data.episode_id.toString(),
          version: data.version,
          auditStatus: data.audit_status,
          scopeList: data.scope_list,
          auditMsg: data.audit_msg,
        }
      }

      case DouyinCallbackTypeEnum.UPLOAD_VIDEO: {
        const data = callbackData as UploadVideoCallbackData
        return {
          ...baseData,
          // 对于视频上传，我们可能需要通过 open_video_id 关联到具体的 episode
          version: null, // 视频上传没有版本概念
          auditStatus: data.success ? 1 : 0, // 成功/失败状态
        }
      }

      default:
        return {
          ...baseData,
          version: parseInt(version) || null,
        }
    }
  }

  /**
   * 查找现有记录
   */
  private async findExistingRecord(callbackType: DouyinCallbackType, callbackData: DouyinCallbackData) {
    const conditions = [eq(douyinAuditRecords.callbackType, callbackType)]

    // 根据回调类型进行查询条件拼接
    switch (callbackType) {
      case DouyinCallbackTypeEnum.ALBUM_AUDIT: {
        const data = callbackData as AlbumAuditCallbackData
        conditions.push(
          eq(douyinAuditRecords.albumId, data.album_id.toString()),
          eq(douyinAuditRecords.version, data.version),
          eq(douyinAuditRecords.auditStatus, data.audit_status),
        )
        break
      }

      case DouyinCallbackTypeEnum.EPISODE_AUDIT: {
        const data = callbackData as EpisodeAuditCallbackData
        conditions.push(
          eq(douyinAuditRecords.episodeId, data.episode_id.toString()),
          eq(douyinAuditRecords.version, data.version),
          eq(douyinAuditRecords.auditStatus, data.audit_status),
        )
        break
      }

      case DouyinCallbackTypeEnum.UPLOAD_VIDEO:
      case DouyinCallbackTypeEnum.POST_REVIEW:
        // 对于这些类型，幂等还未确定
        // 暂时跳过预查询，直接尝试插入
        // TODO: 后续拓展
        return null

      default:
        // 未知类型，跳过预查询
        return null
    }

    return await this.drizzle.db.query.douyinAuditRecords.findFirst({
      where: and(...conditions),
    })
  }

  /**
   * 标记记录为已处理
   */
  async markAsProcessed(recordId: string): Promise<void> {
    await this.drizzle.db.update(douyinAuditRecords).set({ processed: true }).where(eq(douyinAuditRecords.id, recordId))

    this.logger.log(`审核记录已标记为已处理: id=${recordId}`)
  }

  /**
   * 查询未处理的记录
   */
  async findUnprocessedRecords(callbackType?: DouyinCallbackTypeEnum) {
    const conditions = [eq(douyinAuditRecords.processed, false)]

    if (callbackType) {
      conditions.push(eq(douyinAuditRecords.callbackType, callbackType))
    }

    return await this.drizzle.db.query.douyinAuditRecords.findMany({
      where: and(...conditions),
      orderBy: (table, { asc }) => [asc(table.callbackTime)],
    })
  }
}
