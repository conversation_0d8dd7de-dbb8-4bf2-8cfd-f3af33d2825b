import { DrizzleService } from '@/common/drizzle/database.provider'
import { genres } from '@/common/drizzle/schema'
import { BizException } from '@/common/exceptions/biz.exception'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { pageQuery } from '@/common/utils/page-query.util'
import { toResponse } from '@/common/utils/transform.util'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { Injectable } from '@nestjs/common'
import { and, eq, SQLWrapper } from 'drizzle-orm'
import {
  SystemGenresCreateRequest,
  SystemGenresFilterRequest,
  SystemGenresUpdateRequest,
} from '../requests/genres.request'
import { SystemGenresResponse } from '../responses/genres.response'

@Injectable()
export class SystemGenresService {
  constructor(private readonly drizzle: DrizzleService) {}

  async create(data: SystemGenresCreateRequest) {
    const existingGenre = await this.drizzle.db.query.genres.findFirst({
      where: (genres, { eq }) => eq(genres.name, data.name),
    })

    if (existingGenre) {
      throw new BizException(ErrorCodeEnum.GENRES_NAME_EXISTED)
    }

    const [{ id }] = await this.drizzle.db.insert(genres).values(data).$returningId()
    return id
  }

  async update(id: number, data: SystemGenresUpdateRequest) {
    const [{ affectedRows }] = await this.drizzle.db.update(genres).set(data).where(eq(genres.id, id))
    return affectedRows > 0
  }

  async delete(id: number) {
    const [{ affectedRows }] = await this.drizzle.db.delete(genres).where(eq(genres.id, id))
    return affectedRows > 0
  }

  async findOne(id: number) {
    const data = await this.drizzle.db.query.genres.findFirst({ where: (genres, { eq }) => eq(genres.id, id) })
    const result = toResponse(SystemGenresResponse, data)
    return result
  }

  async findAll(paginationRequest: PaginationRequest, filter?: SystemGenresFilterRequest) {
    const buildCondition = (filter?: SystemGenresFilterRequest) => {
      const wheres: SQLWrapper[] = []
      if (filter?.status) {
        wheres.push(eq(genres.status, filter.status))
      }
      return and(...wheres)
    }

    const { getData } = pageQuery(this.drizzle.db, genres, paginationRequest, {
      condition: () => buildCondition(filter),
    })
    const { data, total } = await getData()
    const list = toResponse(SystemGenresResponse, data)
    return PaginationResponse.fromPaginationRequest(list, total, paginationRequest)
  }
}
