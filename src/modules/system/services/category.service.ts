import { DrizzleService } from '@/common/drizzle/database.provider'
import { categories } from '@/common/drizzle/schema'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { pageQuery } from '@/common/utils/page-query.util'
import { toResponse } from '@/common/utils/transform.util'
import { Injectable } from '@nestjs/common'
import { plainToInstance } from 'class-transformer'
import { desc, eq } from 'drizzle-orm'
import { CategoryCreateRequest, CategoryUpdateRequest } from '../requests/category.request'
import { CategoryResponse } from '../responses/category.response'

@Injectable()
export class SystemCategoryService {
  constructor(private readonly drizzle: DrizzleService) {}

  async getById(id: string) {
    const category = await this.drizzle.db.query.categories.findFirst({
      where: (categories, { eq }) => eq(categories.id, id),
    })

    const data = plainToInstance(CategoryResponse, category, {
      enableImplicitConversion: true,
    })

    return data
  }

  async getByPage(paginationRequest: PaginationRequest) {
    const { getData } = pageQuery(this.drizzle.db, categories, paginationRequest, {
      orderBy: () => desc(categories.orderIndex),
    })

    const { data, total } = await getData()

    const list = toResponse(CategoryResponse, data)

    return PaginationResponse.fromPaginationRequest(list, total, paginationRequest)
  }

  async deleteById(id: string) {
    const [{ affectedRows }] = await this.drizzle.db.delete(categories).where(eq(categories.id, id))
    return affectedRows > 0
  }

  async create(data: CategoryCreateRequest) {
    const [{ id }] = await this.drizzle.db.insert(categories).values(data).$returningId()
    return id
  }

  async update(id: string, data: CategoryUpdateRequest) {
    const [{ affectedRows }] = await this.drizzle.db.update(categories).set(data).where(eq(categories.id, id))
    return affectedRows > 0
  }
}
