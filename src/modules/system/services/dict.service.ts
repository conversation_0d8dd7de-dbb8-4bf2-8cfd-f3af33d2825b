import { Injectable } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { systemDicts } from '@/common/drizzle/schema'
import { eq, and, SQL } from 'drizzle-orm'
import { pageQuery } from '@/common/utils/page-query.util'
import { toResponse } from '@/common/utils/transform.util'
import { SystemDictCreateRequest, SystemDictUpdateRequest, SystemDictFilterRequest } from '../requests/dict.request'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { SystemDictResponse } from '../responses/dict.response'
import { PaginationResponse } from '@/common/responses/pagination.response'

@Injectable()
export class SystemDictService {
  constructor(private readonly drizzle: DrizzleService) {}

  async findAll(pagination: PaginationRequest, filter: SystemDictFilterRequest) {
    const buildCondition = (): SQL | undefined => {
      const wheres: import('drizzle-orm').SQL[] = []
      if (filter.type) wheres.push(eq(systemDicts.type, filter.type))
      if (filter.key) wheres.push(eq(systemDicts.key, filter.key))
      if (filter.status !== undefined) wheres.push(eq(systemDicts.status, filter.status))
      return wheres.length ? and(...wheres) : undefined
    }
    const { getData } = pageQuery(this.drizzle.db, systemDicts, pagination, { condition: buildCondition })
    const { data, total } = await getData()
    const list = toResponse(SystemDictResponse, data)
    return PaginationResponse.fromPaginationRequest(list, total, pagination)
  }

  async findOne(id: string) {
    const dict = await this.drizzle.db.query.systemDicts.findFirst({ where: (d, { eq }) => eq(d.id, id) })
    return dict ? toResponse(SystemDictResponse, dict) : null
  }

  async create(dto: SystemDictCreateRequest) {
    const [{ id }] = await this.drizzle.db.insert(systemDicts).values(dto).$returningId()
    return id
  }

  async update(id: string, dto: SystemDictUpdateRequest) {
    const [{ affectedRows }] = await this.drizzle.db.update(systemDicts).set(dto).where(eq(systemDicts.id, id))
    return affectedRows > 0
  }

  async delete(id: string) {
    const [{ affectedRows }] = await this.drizzle.db.delete(systemDicts).where(eq(systemDicts.id, id))
    return affectedRows > 0
  }
}
