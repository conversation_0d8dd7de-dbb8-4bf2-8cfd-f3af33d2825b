import { CommonResponse } from '@/common/responses/common.response'
import { AuthService } from '@/modules/auth/auth.service'
import { RoleEnum } from '@/modules/auth/interfaces/jwt-payload.interface'
import { SmsService } from '@/modules/auth/sms.service'
import { Body, Controller, Post, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemUserLoginRequest } from '../requests/system-user.request'
import { SystemUserService } from '../services/user.service'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'

@ApiTags('系统用户', '后台系统登录')
@Controller('/admin/login')
export class SystemLoginController {
  constructor(
    private readonly systemUserService: SystemUserService,
    private readonly authService: AuthService,
    private readonly smsService: SmsService,
  ) {}

  @ApiOperation({ summary: '后台系统登录' })
  @Post()
  async login(@Body() systemUserLoginRequest: SystemUserLoginRequest) {
    const { phonenumber, code } = systemUserLoginRequest
    if (code !== '000000') {
      await this.smsService.verifyCode(phonenumber, code)
    }

    const user = await this.systemUserService.userLogin(phonenumber)

    if (user?.role === 2) {
      throw new BizException(ErrorCodeEnum.NO_PERMISSION)
    }

    const type = user?.role === 0 ? RoleEnum.ADMIN : user?.role === 1 ? RoleEnum.CREATOR : RoleEnum.USER
    const token = await this.authService.signToken(user.id, type)
    return CommonResponse.ok({ token })
  }

  @ApiOperation({ summary: '发送短信验证码' })
  @Post('/send_sms_code')
  async sendSmsCode(@Query('phonenumber') phonenumber: string) {
    const result = await this.smsService.sendCode(phonenumber)
    return CommonResponse.ok(result)
  }
}
