import { Auth } from '@/common/decorators/auth.decorator'
import { CurrentUser } from '@/common/decorators/current-user.decorator'
import { UserModel } from '@/common/drizzle/schema/system-users'
import { CommonResponse } from '@/common/responses/common.response'
import { Controller, Get } from '@nestjs/common'
import { ApiTags } from '@nestjs/swagger'
import { SystemUserService } from '../services/user.service'

@ApiTags('用户信息')
@Auth()
@Controller('/admin/user-info')
export class SystemUserInfoController {
  constructor(private readonly systemUserService: SystemUserService) {}

  @Get('/me')
  getUserInfo(@CurrentUser() user: UserModel) {
    const response = this.systemUserService.getUserInfoWithCreatorStatus(user)
    return CommonResponse.ok(response)
  }
}
