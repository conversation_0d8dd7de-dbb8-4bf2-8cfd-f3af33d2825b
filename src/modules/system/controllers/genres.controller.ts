import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Param, ParseIntPipe, Post, Put, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemGenresService } from '../services/genres.service'
import {
  SystemGenresCreateRequest,
  SystemGenresFilterRequest,
  SystemGenresUpdateRequest,
} from '../requests/genres.request'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { Auth } from '@/common/decorators/auth.decorator'
import { Admin } from '@/common/decorators/roles.decorator'

@ApiTags('类目管理')
@Auth()
@Admin()
@Controller('/admin/genres')
export class SystemGenresController {
  constructor(private readonly genresService: SystemGenresService) {}

  @ApiOperation({ summary: '获取类目列表' })
  @Get()
  async findAll(
    @Query() paginationRequest: PaginationRequest,
    @Query() systemGenresFilterRequest: SystemGenresFilterRequest,
  ) {
    const result = await this.genresService.findAll(paginationRequest, systemGenresFilterRequest)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '获取类目详情' })
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    console.log(id)
    const result = await this.genresService.findOne(id)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '创建类目' })
  @Post()
  async create(@Body() systemGenresCreateRequest: SystemGenresCreateRequest) {
    const id = await this.genresService.create(systemGenresCreateRequest)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '更新类目' })
  @Put(':id')
  async update(@Param('id', ParseIntPipe) id: number, @Body() systemGenresUpdateRequest: SystemGenresUpdateRequest) {
    const result = await this.genresService.update(id, systemGenresUpdateRequest)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '删除类目' })
  @Delete(':id')
  async delete(@Param('id', ParseIntPipe) id: number) {
    const result = await this.genresService.delete(id)
    return CommonResponse.ok(result)
  }
}
