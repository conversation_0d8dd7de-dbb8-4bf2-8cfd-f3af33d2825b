import { Auth } from '@/common/decorators/auth.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemDictService } from '../services/dict.service'
import { SystemDictCreateRequest, SystemDictUpdateRequest, SystemDictFilterRequest } from '../requests/dict.request'
import { PaginationRequest } from '@/common/requests/pagination.request'

@ApiTags('系统字典')
@Auth()
@Controller('/admin/dict')
export class SystemDictController {
  constructor(private readonly systemDictService: SystemDictService) {}

  @ApiOperation({ summary: '获取字典列表' })
  @Get()
  async findAll(@Query() pagination: PaginationRequest, @Query() filter: SystemDictFilterRequest) {
    const result = await this.systemDictService.findAll(pagination, filter)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '获取字典详情' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.systemDictService.findOne(id)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '创建字典' })
  @Post()
  async create(@Body() dto: SystemDictCreateRequest) {
    const id = await this.systemDictService.create(dto)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '更新字典' })
  @Put(':id')
  async update(@Param('id') id: string, @Body() dto: SystemDictUpdateRequest) {
    const ok = await this.systemDictService.update(id, dto)
    return CommonResponse.ok({ ok })
  }

  @ApiOperation({ summary: '删除字典' })
  @Delete(':id')
  async delete(@Param('id') id: string) {
    const ok = await this.systemDictService.delete(id)
    return CommonResponse.ok({ ok })
  }
}
