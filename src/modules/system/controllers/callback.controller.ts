import { BizException } from '@/common/exceptions/biz.exception'
import { LockService } from '@/common/redis/lock.service'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { DOUYIN_DRAMA_QUEUE_NAME } from '@/constants/processor.contants'
import { InjectQueue } from '@nestjs/bullmq'
import { Body, Controller, Get, Logger, Post } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { Queue } from 'bullmq'
import { praseJson } from 'src/common/utils/json.util'
import {
  DouyinCallbackData,
  DouyinCallbackResponse,
  DouyinCallbackType,
  DouyinCallbackTypeEnum,
  AuditRecord,
} from '../interfaces/douyin-callback.interface'
import { DouyinDramaManagementProvider } from '../providers/douyin-drama-management.provider'
import { DouyinCallbackRequest } from '../requests/callback.request'
import { DouyinAuditRecordService } from '../services/douyin-audit-record.service'
import { DouyinCallbackService } from '../services/douyin-callback.service'
import { DrizzleService } from './../../../common/drizzle/database.provider'

@ApiTags('抖音回调')
@Controller('/callback')
export class CallBackController {
  constructor(
    private readonly logger: Logger,
    private readonly drizzle: DrizzleService,
    private readonly douyinDramaManagementProvider: DouyinDramaManagementProvider,
    private readonly lockService: LockService,
    private readonly auditRecordService: DouyinAuditRecordService,
    private readonly callbackService: DouyinCallbackService,
    @InjectQueue(DOUYIN_DRAMA_QUEUE_NAME) private processorQueue: Queue,
  ) {}

  @Post('/douyin')
  @ApiOperation({ summary: '抖音回调接口' })
  async douyinCallback(@Body() body: DouyinCallbackRequest): Promise<DouyinCallbackResponse> {
    const { type, version, msg } = body

    this.logger.log(`收到抖音回调: type=${type}, version=${version}`)

    // 1. 版本检查
    if (version !== '2.0') {
      throw new BizException(ErrorCodeEnum.DY_CALLBACK_VERSION_NOT_SUPPORTED)
    }

    // 2. 解析回调数据
    const callbackData = praseJson<DouyinCallbackData>(msg)

    // 3. 验证回调类型
    if (!Object.values(DouyinCallbackTypeEnum).includes(type as DouyinCallbackTypeEnum)) {
      this.logger.warn(`未知的回调类型: ${type}`)
      // 对于未知类型，我们仍然返回成功，避免抖音重试
      return { err_no: 0, err_tips: 'success' }
    }

    // 4. 存储审核记录（幂等性处理）
    const auditRecord = await this.auditRecordService.createRecord({
      callbackType: type,
      callbackData,
      version,
    })

    this.logger.log(`审核记录处理: recordId=${auditRecord.id}, isNew=${auditRecord.isNew}`)

    // 5. 如果是新记录，触发事件处理
    if (auditRecord.isNew) {
      await this.handleCallbackEvent(type, auditRecord)
      this.logger.log(`处理回调事件: ${type}`)
    }

    // 6. 返回成功确认
    return {
      err_no: 0,
      err_tips: 'success',
    }
  }

  /**
   * 处理回调事件
   */

  private async handleCallbackEvent(type: DouyinCallbackType, auditRecord: AuditRecord) {
    try {
      switch (type) {
        case DouyinCallbackTypeEnum.ALBUM_AUDIT:
          await this.callbackService.handleAlbumAudit(auditRecord)
          break
        case DouyinCallbackTypeEnum.EPISODE_AUDIT:
          await this.callbackService.handleEpisodeAudit(auditRecord)
          break
        case DouyinCallbackTypeEnum.UPLOAD_VIDEO:
          await this.callbackService.handleVideoUpload(auditRecord)
          break
        case DouyinCallbackTypeEnum.POST_REVIEW:
          await this.callbackService.handlePostReview(auditRecord)
          break
        default:
          this.logger.warn(`未处理的回调类型: ${String(type)}`)
      }
    } catch (error) {
      this.logger.error(`处理回调事件失败: type=${type}`, error)
      // 注意：这里不抛出异常，避免影响回调确认
    }
  }

  /**
   * 测试回调接口
   */
  @Post('/test')
  @ApiOperation({ summary: '测试回调接口' })
  async testCallback(@Body() body: any): Promise<any> {
    this.logger.log('收到测试回调请求', body)

    // 模拟剧审核回调
    const testAlbumAuditCallback: DouyinCallbackRequest = {
      type: 'album_audit',
      version: '2.0',
      msg: JSON.stringify({
        ma_app_id: 'tt498f3f32d6xxxxxxxx',
        album_id: '7270100931638329856',
        version: 1,
        audit_status: 2, // 审核通过
        scope_list: ['可播放', '可挂载'],
        audit_msg: '审核通过',
      }),
    }

    return await this.douyinCallback(testAlbumAuditCallback)
  }

  @Get('/test-lock')
  async testQueueWithTransaction() {}
}
