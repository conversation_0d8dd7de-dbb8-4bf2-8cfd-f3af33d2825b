import { Auth } from '@/common/decorators/auth.decorator'
import { AdminOrCreator } from '@/common/decorators/roles.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Logger, Param, Post } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { CreateEpisodeRequest } from '../requests/episode.request'
import { DramaEpisodeService } from '../services/drama-episode.service'

@ApiTags('分集管理')
@Auth()
@Controller('/admin/dramas')
export class EpisodeController {
  private readonly logger = new Logger(EpisodeController.name)

  constructor(private readonly dramaEpisodeService: DramaEpisodeService) {}

  @Post('/:dramaId/episodes')
  @AdminOrCreator()
  @ApiOperation({ summary: '添加分集' })
  async createEpisode(@Param('dramaId') dramaId: string, @Body() createRequest: CreateEpisodeRequest) {
    this.logger.log(`收到添加分集请求: dramaId=${dramaId}, seq=${createRequest.seq}, title=${createRequest.title}`)

    const result = await this.dramaEpisodeService.createEpisode(dramaId, createRequest)

    this.logger.log(`分集添加成功: dramaId=${dramaId}, seq=${createRequest.seq}, newVersion=${result.newVersion}`)
    return CommonResponse.ok(result)
  }
}
