import { Auth } from '@/common/decorators/auth.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Param, Post, Put } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemConfigService } from '../services/config.service'
import { SystemConfigCreateRequest, SystemConfigUpdateRequest } from '../requests/config.request'

@ApiTags('系统配置')
@Auth()
@Controller('/admin/config')
export class SystemConfigController {
  constructor(private readonly systemConfigService: SystemConfigService) {}

  @ApiOperation({ summary: '获取配置列表' })
  @Get()
  async findAll() {
    const result = await this.systemConfigService.findAll()
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '获取配置详情' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.systemConfigService.findOne(id)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '创建配置' })
  @Post()
  async create(@Body() dto: SystemConfigCreateRequest) {
    const id = await this.systemConfigService.create(dto)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '更新配置' })
  @Put(':id')
  async update(@Param('id') id: string, @Body() dto: SystemConfigUpdateRequest) {
    await this.systemConfigService.update(id, dto)
    return CommonResponse.ok()
  }

  @ApiOperation({ summary: '删除配置' })
  @Delete(':id')
  async delete(@Param('id') id: string) {
    await this.systemConfigService.delete(id)
    return CommonResponse.ok()
  }
}
