import { PaginationRequest } from '@/common/requests/pagination.request'
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemCategoryService } from '../services/category.service'
import { CommonResponse } from '@/common/responses/common.response'
import { CategoryCreateRequest, CategoryUpdateRequest } from '../requests/category.request'
import { Auth } from '@/common/decorators/auth.decorator'
import { Admin } from '@/common/decorators/roles.decorator'

@ApiTags('类别管理')
@Auth()
@Admin()
@Controller('/admin/category')
export class SystemCategoryController {
  constructor(private readonly systemCategoryService: SystemCategoryService) {}

  @ApiOperation({ summary: '获取类别列表' })
  @Get()
  async findAll(@Query() paginationRequest: PaginationRequest) {
    const result = await this.systemCategoryService.getByPage(paginationRequest)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '获取类别详情' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const data = await this.systemCategoryService.getById(id)
    return CommonResponse.ok(data)
  }

  @ApiOperation({ summary: '创建类别' })
  @Post()
  async create(@Body() categoryCreateRequest: CategoryCreateRequest) {
    const id = await this.systemCategoryService.create(categoryCreateRequest)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '更新类别' })
  @Put(':id')
  async update(@Param('id') id: string, @Body() categoryUpdateRequest: CategoryUpdateRequest) {
    const result = await this.systemCategoryService.update(id, categoryUpdateRequest)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '删除类别' })
  @Delete(':id')
  async delete(@Param('id') id: string) {
    const result = await this.systemCategoryService.deleteById(id)
    return CommonResponse.ok(result)
  }
}
