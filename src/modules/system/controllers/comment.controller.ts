import { Auth } from '@/common/decorators/auth.decorator'
import { CurrentUser } from '@/common/decorators/current-user.decorator'
import { UserModel } from '@/common/drizzle/schema/system-users'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemCommentService } from '../services/comment.service'
import { PaginationRequest } from '@/common/requests/pagination.request'
import {
  SystemCommentCreateRequest,
  SystemCommentUpdateRequest,
  SystemCommentFilterRequest,
  SystemCommentSetFeaturedRequest,
} from '../requests/comment.request'

@ApiTags('评论管理')
@Auth()
@Controller('/admin/comment')
export class SystemCommentController {
  constructor(private readonly systemCommentService: SystemCommentService) {}

  @ApiOperation({ summary: '获取评论列表' })
  @Get()
  async findAll(
    @Query() pagination: PaginationRequest,
    @Query() filter: SystemCommentFilterRequest,
    @CurrentUser() user: UserModel,
  ) {
    const result = await this.systemCommentService.findAll(pagination, filter, user.id, user.role)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '获取评论详情' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.systemCommentService.findOne(id)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '创建评论' })
  @Post()
  async create(@Body() dto: SystemCommentCreateRequest, @CurrentUser() user: UserModel) {
    const id = await this.systemCommentService.create(dto, user.id, user.role)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '更新评论' })
  @Put(':id')
  async update(@Param('id') id: string, @Body() dto: SystemCommentUpdateRequest, @CurrentUser() user: UserModel) {
    const ok = await this.systemCommentService.update(id, dto, user.id, user.role)
    return CommonResponse.ok({ ok })
  }

  @ApiOperation({ summary: '删除评论' })
  @Delete(':id')
  async delete(@Param('id') id: string, @CurrentUser() user: UserModel) {
    const ok = await this.systemCommentService.delete(id, user.id, user.role)
    return CommonResponse.ok({ ok })
  }

  @ApiOperation({ summary: '设置精选评论' })
  @Put(':id/featured')
  async setFeatured(
    @Param('id') id: string,
    @Body() dto: SystemCommentSetFeaturedRequest,
    @CurrentUser() user: UserModel,
  ) {
    const ok = await this.systemCommentService.setFeatured(id, dto.isFeatured, user.id, user.role)
    return CommonResponse.ok({ ok })
  }
}
