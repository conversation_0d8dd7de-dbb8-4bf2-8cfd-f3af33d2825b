import { Auth } from '@/common/decorators/auth.decorator'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import {
  SystemUserCreateRequest,
  SystemUserFilterRequest,
  SystemUserUpdateRequest,
} from '../requests/system-user.request'
import { SystemUserService } from '../services/user.service'
import { Admin } from '@/common/decorators/roles.decorator'

@ApiTags('系统用户')
@Auth()
@Admin()
@Controller('/admin/user')
export class SystemUserController {
  constructor(private readonly systemUserService: SystemUserService) {}

  @ApiOperation({ summary: '获取用户列表' })
  @Get()
  async getUserList(@Query() paginationRequest: PaginationRequest, @Query() filter: SystemUserFilterRequest) {
    const users = await this.systemUserService.findAll(paginationRequest, filter)
    return CommonResponse.ok(users)
  }

  @ApiOperation({ summary: '创建用户' })
  @Post()
  async createUser(@Body() systemCreateRequest: SystemUserCreateRequest) {
    const id = await this.systemUserService.create(systemCreateRequest)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '获取用户详情' })
  @Get('/:id')
  async getUserById(@Param('id') id: string) {
    const user = await this.systemUserService.findOne(id)
    return CommonResponse.ok(user)
  }

  @ApiOperation({ summary: '更新用户' })
  @Put('/:id')
  async updateUserById(@Param('id') id: string, @Body() systemUpdateRequest: SystemUserUpdateRequest) {
    const result = await this.systemUserService.update(id, systemUpdateRequest)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '删除用户' })
  @Delete('/:id')
  async deleteUserById(@Param('id') id: string) {
    const result = await this.systemUserService.delete(id)
    return CommonResponse.ok(result)
  }
}
