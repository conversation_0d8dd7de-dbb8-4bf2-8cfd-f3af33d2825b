import { Auth } from '@/common/decorators/auth.decorator'
import { AdminOrCreator } from '@/common/decorators/roles.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Get, Logger, Post, Query, Param } from '@nestjs/common'
import { ApiOperation, ApiTags, ApiQuery, ApiParam } from '@nestjs/swagger'
import { VideoUploadRequest } from '../requests/video-upload.request'
import { VideoUploadService } from '../services/video-upload.service'
import { VideoUploadStatus } from '@/common/drizzle/schema/video-uploads'

@ApiTags('资源上传')
@Auth()
@Controller('/admin/uploads')
export class VideoUploadController {
  private readonly logger = new Logger(VideoUploadController.name)

  constructor(private readonly videoUploadService: VideoUploadService) {}

  @Post('/video')
  @AdminOrCreator()
  @ApiOperation({ summary: '上传视频到抖音平台' })
  async uploadVideo(@Body() request: VideoUploadRequest) {
    this.logger.log(`收到视频上传请求: ${request.title}`)

    const result = await this.videoUploadService.uploadVideo(request)

    this.logger.log(`视频上传请求提交成功: uploadId=${result.id}`)
    return CommonResponse.ok(result)
  }

  @Get('/videos')
  @AdminOrCreator()
  @ApiOperation({ summary: '查询用户的视频上传记录' })
  @ApiQuery({ name: 'status', required: false, enum: VideoUploadStatus, description: '上传状态筛选' })
  async getUserVideoUploads(@Query('status') status?: string) {
    this.logger.log(`查询用户视频上传记录, status=${status}`)

    const statusNum = status !== undefined ? parseInt(status, 10) : undefined
    const result = await this.videoUploadService.getUserVideoUploads(statusNum)

    return CommonResponse.ok(result)
  }

  @Get('/video/:openVideoId')
  @AdminOrCreator()
  @ApiOperation({ summary: '根据抖音视频ID查询上传记录' })
  @ApiParam({ name: 'openVideoId', description: '抖音视频ID', example: 'v0d00fg10000ck9c77og65v6rjqf5h70' })
  async getVideoUploadByOpenVideoId(@Param('openVideoId') openVideoId: string) {
    this.logger.log(`根据openVideoId查询上传记录: ${openVideoId}`)

    const result = await this.videoUploadService.getVideoUploadByOpenVideoId(openVideoId)

    if (!result) {
      this.logger.warn(`未找到openVideoId为${openVideoId}的上传记录`)
      return CommonResponse.ok(null)
    }

    this.logger.log(`查询成功: uploadId=${result.id}, status=${result.status}`)
    return CommonResponse.ok(result)
  }
}
