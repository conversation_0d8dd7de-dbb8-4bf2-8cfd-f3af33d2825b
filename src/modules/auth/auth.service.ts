import { DrizzleService } from '@/common/drizzle/database.provider'
import { Injectable, Logger } from '@nestjs/common'
import { JwtService } from '@nestjs/jwt'
import { JwtPayload, RoleEnum } from './interfaces/jwt-payload.interface'
import { CacheService } from '@/common/redis/cache.service'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'

@Injectable()
export class AuthService {
  constructor(
    private readonly drizzle: DrizzleService,
    private readonly jwtService: JwtService,
    private readonly cacheService: CacheService,
    private readonly logger: Logger,
  ) {}

  get jwtServicePublic() {
    return this.jwtService
  }

  async signToken(id: string, type: RoleEnum) {
    const payload: JwtPayload = {
      id,
      type,
    }
    return this.jwtService.signAsync(payload)
  }

  async verifyToken(token: string): Promise<JwtPayload> {
    try {
      const payload: JwtPayload = await this.jwtService.verifyAsync(token)
      return payload
    } catch (e) {
      this.logger.error(e)
      throw new BizException(ErrorCodeEnum.LOGIN_FAILED)
    }
  }
}
