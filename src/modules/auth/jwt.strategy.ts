import { ExtractJwt, Strategy } from 'passport-jwt'

import { JWT_SECRET } from '@/app.config'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { Injectable } from '@nestjs/common'
import { PassportStrategy } from '@nestjs/passport'
import { JwtPayload } from './interfaces/jwt-payload.interface'
import { BizException } from '@/common/exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly drizzle: DrizzleService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: JWT_SECRET,
    })
  }

  async validate(payload: JwtPayload) {
    const { id } = payload
    const user = await this.drizzle.db.query.systemUsers.findFirst({
      where: (systemUser, { eq, and }) => and(eq(systemUser.id, id), eq(systemUser.isDeleted, 0)),
    })
    if (!user) {
      throw new BizException(ErrorCodeEnum.USER_NOT_FOUND)
    }
    return payload
  }
}
