import { genrate<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/constants/cache.constants'
import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { BizException } from '@/common/exceptions/biz.exception'
import { CacheService } from '@/common/redis/cache.service'
import { Injectable, Logger } from '@nestjs/common'
import { sms } from '@volcengine/openapi'
import {
  SMS_ACCOUNT,
  SMS_CACHE_TIME,
  SMS_SIGN,
  SMS_TEMPLATE_ID,
  VOLCENGINE_ACCESS_KEY_ID,
  VOLCENGINE_SECRET_ACCESS_KEY,
} from '@/app.config'

@Injectable()
export class SmsService {
  private smsService: sms.SmsService

  constructor(
    private readonly cacheService: CacheService,
    private readonly logger: Logger,
  ) {
    this.smsService = sms.defaultService
    this.smsService.setAccessKeyId(VOLCENGINE_ACCESS_KEY_ID)
    this.smsService.setSecretKey(VOLCENGINE_SECRET_ACCESS_KEY)
  }

  async sendCode(phoneNumber: string) {
    const key = genrateKey(RedisKeys.SMS_CODE_KEY, phoneNumber)

    const hasCode = await this.cacheService.get(key)
    if (hasCode) {
      throw new BizException(ErrorCodeEnum.SMS_REPEATED)
    }

    const code = this.generateCode()
    const sendResponse = await this.smsService.Send({
      SmsAccount: SMS_ACCOUNT,
      Sign: SMS_SIGN,
      TemplateID: SMS_TEMPLATE_ID,
      PhoneNumbers: phoneNumber,
      TemplateParam: JSON.stringify({ code }),
      Tag: phoneNumber,
      UserExtCode: phoneNumber,
    })
    this.logger.log(`发送短信验证码，手机号：${phoneNumber}，验证码：${code}`)
    await this.cacheService.set(key, code, SMS_CACHE_TIME)
    return !!sendResponse.ResponseMetadata.Error
  }

  async verifyCode(phoneNumber: string, code: string) {
    const key = genrateKey(RedisKeys.SMS_CODE_KEY, phoneNumber)
    const hasCode = await this.cacheService.get(key)
    if (!hasCode) {
      throw new BizException(ErrorCodeEnum.SMS_CODE_NOT_EXIST)
    }
    return hasCode === code
  }

  async removeCode(phoneNumber: string) {
    const key = genrateKey(RedisKeys.SMS_CODE_KEY, phoneNumber)
    return this.cacheService.getClient().delete(key)
  }

  private generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString()
  }
}
