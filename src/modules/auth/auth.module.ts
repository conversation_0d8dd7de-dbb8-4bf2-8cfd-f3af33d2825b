import { JWT_EXPIRE, JWT_SECRET } from '@/app.config'
import { Global, Module } from '@nestjs/common'
import { JwtModule } from '@nestjs/jwt'
import { PassportModule } from '@nestjs/passport'
import { AuthService } from './auth.service'
import { JwtStrategy } from './jwt.strategy'
import { SmsService } from './sms.service'

const jwtModule = JwtModule.registerAsync({
  useFactory() {
    return {
      secret: JWT_SECRET,
      signOptions: {
        expiresIn: JWT_EXPIRE,
        algorithm: 'HS256',
      },
    }
  },
})

@Global()
@Module({
  imports: [PassportModule, jwtModule],
  providers: [AuthService, SmsService, JwtStrategy],
  exports: [JwtStrategy, SmsService, AuthService, jwtModule],
})
export class AuthModule {}
