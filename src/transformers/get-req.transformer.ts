import { SystemUserSelectType } from '@/common/drizzle/schema/system-users'
import { ExecutionContext } from '@nestjs/common'
import { Request } from 'express'

type BizRequest = {
  user?: SystemUserSelectType
  token?: string
}

export type ExpressBizRequest = Request & BizRequest

export function getNestExecutionContextRequest(context: ExecutionContext): ExpressBizRequest {
  return context.switchToHttp().getRequest<ExpressBizRequest>()
}
