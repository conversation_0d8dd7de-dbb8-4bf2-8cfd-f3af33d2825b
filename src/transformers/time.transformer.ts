import { applyDecorators } from '@nestjs/common'
import { Transform } from 'class-transformer'
import dayjs from 'dayjs'

export const DEFAULT_DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss'
export const DEFAULT_DATE_FORMAT = 'YYYY-MM-DD'

type ValueType = string | number | Date | undefined

export function DateTimeFormat(format = DEFAULT_DATETIME_FORMAT) {
  const decorators: (ClassDecorator | PropertyDecorator | MethodDecorator)[] = [
    Transform(({ value }: { value: ValueType }) => (value ? dayjs(value).format(format) : value)),
  ]
  return applyDecorators(...decorators)
}

export const DateFormat = () => DateTimeFormat(DEFAULT_DATE_FORMAT)
