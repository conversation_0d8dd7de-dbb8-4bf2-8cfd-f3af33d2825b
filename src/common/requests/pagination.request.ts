import { ApiProperty, ApiSchema } from '@nestjs/swagger'
import { Transform } from 'class-transformer'
import { IsInt, Max, Min, IsOptional, IsString, IsIn } from 'class-validator'

@ApiSchema({ name: 'PaginationRequest' })
export class PaginationRequest {
  @ApiProperty({ description: '当前页', example: 1 })
  @IsInt()
  @Min(1)
  page: number = 1

  @ApiProperty({ description: '每页条数', example: 10 })
  @IsInt()
  @Min(1)
  @Max(100)
  @Transform(({ value }) => Math.min(Number(value) || 10, 100))
  size: number = 10

  @ApiProperty({ description: '排序字段', required: false, example: 'createdAt' })
  @IsOptional()
  @IsString()
  orderBy?: string

  @ApiProperty({ description: '排序方式', required: false, enum: ['asc', 'desc'], example: 'desc' })
  @IsOptional()
  @IsString()
  @IsIn(['asc', 'desc'])
  @Transform(({ value }) => String(value)?.toLowerCase())
  order: 'asc' | 'desc' = 'desc'
}
