import { ErrorCode, ErrorCodeEnum } from '@/constants/error-code.constant'
import { HttpException } from '@nestjs/common'

export class BizException extends HttpException {
  // 1. 在这里声明一个公共的 code 属性
  public code: ErrorCodeEnum
  constructor(codeOrMessage: ErrorCodeEnum | string) {
    if (typeof codeOrMessage === 'string') {
      const code = ErrorCodeEnum.DOUYIN_API_ERROR
      const [, status] = ErrorCode[code]
      const douyinPreMsg = '抖音调用提示：'
      super(HttpException.createBody({ code, message: douyinPreMsg + codeOrMessage }), Number(status))
      // 2. 给实例的属性赋值
      this.code = code
    } else {
      const [message, status] = ErrorCode[codeOrMessage]
      super(HttpException.createBody({ code: codeOrMessage, message }), status)
      // 2. 给实例的属性赋值
      this.code = codeOrMessage
    }
  }
}
