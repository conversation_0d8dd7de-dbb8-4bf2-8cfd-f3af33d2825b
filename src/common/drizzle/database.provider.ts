import { DATABASE_URL } from '@/app.config'
import { logger } from '@/global/logger.global'
import { Injectable } from '@nestjs/common'
import { drizzle } from 'drizzle-orm/mysql2'
import { createPool } from 'mysql2/promise'
import * as schema from './schema'

export type DrizzleDB = ReturnType<typeof drizzle<typeof schema>>

@Injectable()
export class DrizzleService {
  private readonly _db: DrizzleDB
  constructor() {
    const uri = DATABASE_URL
    const pool = createPool({ uri, waitForConnections: true, connectionLimit: 10, queueLimit: 0 })
    this._db = drizzle({
      client: pool,
      schema,
      mode: 'default',
      logger: {
        logQuery(query, params) {
          logger.info(query, params)
        },
      },
    }) as unknown as DrizzleDB
  }

  get db() {
    return this._db
  }
}

export const drizzleProvider = {
  provide: DrizzleService,
  useClass: DrizzleService,
}
