import { datetime, index, int, mysqlTable, text, unique, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'

// 字典表
export const systemDicts = mysqlTable(
  'system_dict',
  {
    id: varchar('id', { length: 100 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => KSUID.randomSync().string),
    type: varchar('type', { length: 50 }).notNull(), // 字典类型
    key: varchar('key', { length: 50 }).notNull(), // 字典键
    value: varchar('value', { length: 255 }).notNull(), // 字典值
    order: int('order').default(0), // 显示顺序
    status: int('status').default(1), // 状态（0:禁用, 1:启用）
    remark: text('remark'), // 备注
    createdAt: datetime('created_at').$default(() => new Date()),
    updatedAt: datetime('updated_at')
      .$default(() => new Date())
      .$onUpdate(() => new Date()),
  },
  (table) => [unique().on(table.type, table.key), index('idx_type').on(table.type)],
)
