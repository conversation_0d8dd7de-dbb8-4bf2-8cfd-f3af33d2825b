import { datetime, int, mysqlTable, varchar, uniqueIndex } from 'drizzle-orm/mysql-core'
import { relations } from 'drizzle-orm'
import KSUID from 'ksuid'
import { comments } from './comments'
import { systemUsers } from './system-users'

// 评论点赞表
export const commentLikes = mysqlTable(
  'comment_likes',
  {
    id: varchar('id', { length: 100 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => KSUID.randomSync().string),
    userId: varchar('user_id', { length: 100 })
      .notNull()
      .references(() => systemUsers.id), // 点赞用户ID
    commentId: varchar('comment_id', { length: 100 })
      .notNull()
      .references(() => comments.id), // 评论ID
    status: int('status').notNull().default(1), // 点赞状态：0-取消点赞 1-点赞
    createdAt: datetime('created_at').$default(() => new Date()),
    updatedAt: datetime('updated_at')
      .$default(() => new Date())
      .$onUpdate(() => new Date()),
  },
  (table) => [
    // 用户对同一评论只能有一条记录
    uniqueIndex('uk_user_comment').on(table.userId, table.commentId),
  ],
)

// 定义点赞与用户、评论的关系
export const commentLikesRelations = relations(commentLikes, ({ one }) => ({
  user: one(systemUsers, {
    fields: [commentLikes.userId],
    references: [systemUsers.id],
  }),
  comment: one(comments, {
    fields: [commentLikes.commentId],
    references: [comments.id],
  }),
}))
