import { relations } from 'drizzle-orm'
import { datetime, mysqlTable, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { systemUsers } from './system-users'
import { dramas } from './dramas'

// 用户收藏表
export const userFavorites = mysqlTable('user_favorites', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  userId: varchar('user_id', { length: 100 }).notNull(), // 用户ID
  dramaId: varchar('drama_id', { length: 100 }).notNull(), // 短剧ID
  createdAt: datetime('created_at').$default(() => new Date()),
})

// 定义收藏关系
export const userFavoritesRelations = relations(userFavorites, ({ one }) => ({
  user: one(systemUsers, {
    fields: [userFavorites.userId],
    references: [systemUsers.id],
  }),
  drama: one(dramas, {
    fields: [userFavorites.dramaId],
    references: [dramas.id],
  }),
}))
