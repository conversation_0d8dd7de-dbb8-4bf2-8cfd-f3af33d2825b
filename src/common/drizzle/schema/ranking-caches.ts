import { datetime, int, mysqlTable, varchar, decimal } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'

// 排行榜缓存表
export const rankingCaches = mysqlTable('ranking_cache', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  rankType: varchar('rank_type', { length: 50 }).notNull(), // 排行类型：hot_rank/new_rank/must_watch等
  categoryId: varchar('category_id', { length: 100 }), // 分类ID，为空表示全局排行
  dramaId: varchar('drama_id', { length: 100 }).notNull(), // 短剧ID
  rank: int('rank').notNull(), // 排名
  score: decimal('score', { precision: 10, scale: 2 }).notNull(), // 排序分数
  cacheDate: datetime('cache_date').notNull(), // 缓存日期
  createdAt: datetime('created_at').$default(() => new Date()),
})
