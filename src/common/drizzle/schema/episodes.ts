import { relations } from 'drizzle-orm'
import { datetime, int, mysqlTable, text, varchar, decimal } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { dramas } from './dramas'

// 短剧分集
export const episodes = mysqlTable('episode', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  dramaId: varchar('dramaId', { length: 100 }).notNull(), // 所属短剧id
  title: varchar('title', { length: 100 }).notNull(), // 分集标题
  seq: int('seq').notNull(), // 第几集
  coverUrl: varchar('cover_url', { length: 255 }), // 分集封面图片URL
  coverOpenPicId: varchar('cover_open_pic_id', { length: 100 }), // 抖音封面图片id
  openVideoId: varchar('open_video_id', { length: 100 }), // 抖音视频id
  douyinEpisodeId: varchar('douyin_episode_id', { length: 100 }), // 抖音分集ID
  version: int('version').default(0), // 分集所属版本号
  videoUrl: varchar('video_url', { length: 255 }).notNull(), // 分集视频地址
  videoUploadStatus: int('video_upload_status').default(0), // 视频上传状态 0:上传失败 1:上传成功
  videoOrientation: int('video_orientation').default(1), // 视频方向 1:竖版 2:横版
  description: text('description'), // 分集简介
  duration: varchar('duration', { length: 20 }), // 分集时长
  fileSize: varchar('file_size', { length: 50 }), // 文件大小
  isFree: int('is_free').default(0), // 是否免费 0:付费 1:免费
  price: decimal('price', { precision: 10, scale: 2 }).default('0.00'), // 价格
  viewCount: int('view_count').default(0), // 观看次数
  playbackCount: int('playback_count').default(0), // 播放次数
  createdAt: datetime('created_at').$default(() => new Date()),
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
  isDeleted: int('is_deleted').notNull().default(0), // 0: not deleted, 1: deleted
})

// 定义 短剧 和 分集 之间的关系
export const episodesRelations = relations(episodes, ({ one }) => ({
  drama: one(dramas, {
    fields: [episodes.dramaId],
    references: [dramas.id],
  }),
}))
