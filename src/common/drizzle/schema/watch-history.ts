import { relations } from 'drizzle-orm'
import { datetime, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { systemUsers } from './system-users'
import { dramas } from './dramas'
import { episodes } from './episodes'

// 观看历史表
export const watchHistory = mysqlTable('watch_history', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  userId: varchar('user_id', { length: 100 }).notNull(), // 用户ID
  dramaId: varchar('drama_id', { length: 100 }).notNull(), // 短剧ID
  episodeId: varchar('episode_id', { length: 100 }).notNull(), // 分集ID
  watchProgress: int('watch_progress').default(0), // 观看进度(秒)
  watchDuration: int('watch_duration').default(0), // 观看时长(秒)
  lastWatchAt: datetime('last_watch_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()), // 最后观看时间
  createdAt: datetime('created_at').$default(() => new Date()),
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
})

// 定义关系
export const watchHistoryRelations = relations(watchHistory, ({ one }) => ({
  user: one(systemUsers, {
    fields: [watchHistory.userId],
    references: [systemUsers.id],
  }),
  drama: one(dramas, {
    fields: [watchHistory.dramaId],
    references: [dramas.id],
  }),
  episode: one(episodes, {
    fields: [watchHistory.episodeId],
    references: [episodes.id],
  }),
}))
