import { datetime, mysqlTable, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'

// 系统配置表
export const systemConfigs = mysqlTable('system_config', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  configKey: varchar('config_key', { length: 50 }).notNull().unique(), // 配置键
  configValue: varchar('config_value', { length: 255 }).notNull(), // 配置值
  description: varchar('description', { length: 255 }), // 配置描述
  updatedBy: varchar('updated_by', { length: 100 }), // 更新人
  createdAt: datetime('created_at').$default(() => new Date()),
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
})
