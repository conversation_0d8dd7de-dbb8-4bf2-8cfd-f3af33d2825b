import { mysqlTable, varchar, int, datetime, text } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { relations } from 'drizzle-orm'
import { systemUsers } from './system-users'

// 创作者申请表
export const creatorApplications = mysqlTable('creator_application', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  userId: varchar('user_id', { length: 100 }).notNull(), // 用户ID
  realName: varchar('real_name', { length: 50 }).notNull(), // 真实姓名
  idCard: varchar('id_card', { length: 50 }).notNull(), // 身份证号
  phone: varchar('phone', { length: 20 }).notNull(), // 手机号
  email: varchar('email', { length: 100 }), // 邮箱
  reason: varchar('reason', { length: 500 }).notNull(), // 申请理由
  portfolioUrl: varchar('portfolio_url', { length: 255 }), // 作品集URL
  extraInfo: varchar('extra_info', { length: 500 }), // 额外信息
  attachments: text('attachments'), // 存储文件URL数组 （JSON数组格式存储多张图片URL）
  status: int('status').notNull().default(0), // 0:待审核 1:通过 2:拒绝
  auditRemark: varchar('audit_remark', { length: 255 }), // 审核备注
  auditBy: varchar('audit_by', { length: 100 }), // 审核人
  auditAt: datetime('audit_at'), // 审核时间
  createdAt: datetime('created_at').$default(() => new Date()),
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
})

export const creatorApplicationsRelations = relations(creatorApplications, ({ one }) => ({
  user: one(systemUsers, {
    fields: [creatorApplications.userId],
    references: [systemUsers.id],
  }),
}))
