import { datetime, decimal, int, mysqlTable, text, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { dramas } from './dramas'
import { episodes } from './episodes'

export const orders = mysqlTable('orders', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  orderNo: varchar('order_no', { length: 50 }).notNull(), // 订单编号
  userId: varchar('userId', { length: 100 }).notNull(), // 用户ID
  dramaId: varchar('dramaId', { length: 100 }).references(() => dramas.id, { onDelete: 'set null' }), // 关联到 dramas 表，可选
  episodeId: varchar('episodeId', { length: 100 }).references(() => episodes.id, { onDelete: 'set null' }), // 关联到 episodes 表，可选

  // 价格相关
  orderAmount: decimal('order_amount', { precision: 10, scale: 2 }).notNull().default('0.00'), // 订单总金额
  discountAmount: decimal('discount_amount', { precision: 10, scale: 2 }).default('0.00'), // 折扣金额
  finalAmount: decimal('final_amount', { precision: 10, scale: 2 }).notNull().default('0.00'), // 最终支付金额

  // 支付相关
  status: varchar('status', { length: 50 }).notNull().default('pending'), // 订单状态：pending(待支付), paid(已支付), cancelled(已取消), refunded(已退款)
  paymentMethod: varchar('payment_method', { length: 50 }), // 支付方式
  paymentPlatform: varchar('payment_platform', { length: 50 }), // 支付平台（支付宝、微信等）
  paymentCurrency: varchar('payment_currency', { length: 20 }).notNull().default('CNY'), // 支付货币：CNY(人民币), DIAMOND(钻石)
  clientPlatform: varchar('client_platform', { length: 20 }).notNull().default('ANDROID'), // 客户端平台：ANDROID, IOS
  diamondAmount: int('diamond_amount').default(0), // 钻石数量（iOS平台使用）
  transactionId: varchar('transaction_id', { length: 100 }), // 支付交易ID
  paymentTime: datetime('payment_time'), // 支付时间

  // 退款相关
  refundStatus: varchar('refund_status', { length: 50 }).default('none'), // 退款状态：none(无退款), pending(退款中), completed(已退款), rejected(已拒绝)
  refundAmount: decimal('refund_amount', { precision: 10, scale: 2 }).default('0.00'), // 退款金额
  refundTime: datetime('refund_time'), // 退款时间
  refundReason: text('refund_reason'), // 退款原因

  // 订单信息
  orderSource: varchar('order_source', { length: 50 }), // 订单来源（抖音、微信等）
  orderType: varchar('order_type', { length: 50 }).notNull().default('drama'), // 订单类型：drama(短剧)
  orderNote: text('order_note'), // 订单备注

  // 分成相关
  platformShare: decimal('platform_share', { precision: 5, scale: 2 }).default('0.00'), // 平台分成比例
  creatorShare: decimal('creator_share', { precision: 5, scale: 2 }).default('0.00'), // 创作者分成比例

  // 系统字段
  expireTime: datetime('expire_time'), // 订单过期时间
  createdAt: datetime('created_at').$default(() => new Date()),
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
  isDeleted: int('is_deleted').notNull().default(0), // 软删除标记 0:未删除 1:已删除
})
