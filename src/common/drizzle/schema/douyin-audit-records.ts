import { boolean, datetime, int, json, mysqlTable, text, varchar, index, uniqueIndex } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'

export const douyinAuditRecords = mysqlTable(
  'douyin_audit_records',
  {
    id: varchar('id', { length: 100 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => KSUID.randomSync().string),
    callbackType: varchar('callback_type', { length: 50 }).notNull(), // 回调类型: album_audit, episode_audit, upload_video, post_review
    albumId: varchar('album_id', { length: 100 }), // 短剧ID
    episodeId: varchar('episode_id', { length: 100 }), // 剧集ID
    version: int('version'), // 版本号
    auditStatus: int('audit_status'), // 审核状态
    scopeList: json('scope_list'), // 能力列表
    auditMsg: text('audit_msg'), // 审核备注
    rawCallbackData: json('raw_callback_data').notNull(), // 完整的原始回调数据
    callbackTime: datetime('callback_time').$default(() => new Date()), // 回调时间
    processed: boolean('processed').default(false), // 是否已处理
    createdAt: datetime('created_at').$default(() => new Date()), // 创建时间
    updatedAt: datetime('updated_at')
      .$default(() => new Date())
      .$onUpdate(() => new Date()), // 更新时间
  },
  (table) => [
    // 幂等性唯一索引
    uniqueIndex('uk_album_audit').on(table.albumId, table.version, table.auditStatus, table.callbackType),
    uniqueIndex('uk_episode_audit').on(table.episodeId, table.version, table.auditStatus, table.callbackType),
    // 查询索引
    index('callback_type_idx').on(table.callbackType),
    index('album_id_idx').on(table.albumId),
    index('episode_id_idx').on(table.episodeId),
    index('processed_idx').on(table.processed),
    index('callback_time_idx').on(table.callbackTime),
  ],
)
