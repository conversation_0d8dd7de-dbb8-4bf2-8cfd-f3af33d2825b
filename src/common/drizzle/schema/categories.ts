import { datetime, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'

// 分类表
export const categories = mysqlTable('categories', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  name: varchar('name', { length: 50 }).notNull().unique(),
  description: varchar('description', { length: 255 }),
  orderIndex: int('order_index').notNull(),
  createdAt: datetime('created_at').$default(() => new Date()),
})
