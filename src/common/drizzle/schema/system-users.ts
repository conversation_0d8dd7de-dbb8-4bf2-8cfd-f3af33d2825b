import { relations, type InferSelectModel } from 'drizzle-orm'
import { datetime, int, mysqlTable, varchar } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { dramas } from './dramas'
import { creatorApplications } from './creator-applications'

// 系统用户表
export const systemUsers = mysqlTable('system_user', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  username: varchar('username', { length: 255 }).notNull().unique(),
  password: varchar('password', { length: 255 }),
  nickname: varchar('nickname', { length: 255 }).notNull(),
  email: varchar('email', { length: 100 }).unique(),
  phone: varchar('phone', { length: 20 }).unique(),
  avatar: varchar('avatar', { length: 255 }),
  bio: varchar('bio', { length: 500 }), // 个人简介
  openId: varchar('open_id', { length: 100 }),
  unionId: varchar('union_id', { length: 100 }),
  sessionKey: varchar('session_key', { length: 100 }),
  anonymousOpenid: varchar('anonymous_openid', { length: 100 }),
  role: int('role').notNull().default(2), // 0: admin, 1: creator, 2: user
  status: int('status').notNull().default(1), // 1: active, 0: inactive
  lastLoginAt: datetime('last_login_at'),
  lastLoginIp: varchar('last_login_ip', { length: 255 }),
  createdAt: datetime('created_at').$default(() => new Date()),
  hasCreatorApproval: int('has_creator_approval').notNull().default(0), // 0: not approved, 1: approved
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
  isDeleted: int('is_deleted').notNull().default(0), // 0: not deleted, 1: deleted
})

export const systemUsersRelations = relations(systemUsers, ({ many, one }) => ({
  dramas: many(dramas),
  creatorApplication: one(creatorApplications, {
    fields: [systemUsers.id],
    references: [creatorApplications.userId],
  }),
}))

// 生成对应的typeScript类型
export type SystemUserSelectType = InferSelectModel<typeof systemUsers>

export type UserModel = SystemUserSelectType
