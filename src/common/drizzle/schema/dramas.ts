import { relations } from 'drizzle-orm'
import { datetime, decimal, int, mysqlTable, text, varchar, index } from 'drizzle-orm/mysql-core'
import KSUID from 'ksuid'
import { systemUsers } from './system-users'
import { episodes } from './episodes'

// 短剧上线状态枚举
export enum DramaOnlineStatus {
  ONLINE = 0, // 上线
  OFFLINE = 1, // 下线
}

// 短剧状态枚举
export enum DramaAlbumStatus {
  NOT_RELEASED = 1, // 未上映
  UPDATING = 2, // 更新中
  COMPLETED = 3, // 已完结
}

// 免费试看枚举
export enum DramaFreePreview {
  NO = 0, // 否
  YES = 1, // 是
}

export const dramas = mysqlTable(
  'dramas',
  {
    id: varchar('id', { length: 100 })
      .notNull()
      .primaryKey()
      .$defaultFn(() => KSUID.randomSync().string),
    albumId: varchar('album_id', { length: 100 }), // 抖音短剧ID
    title: varchar('title', { length: 100 }).notNull(), // 短剧标题
    seqNum: int('seq_num').default(0), // 总集数
    coverVertical: varchar('cover_vertical', { length: 255 }), // 竖版封面图片URL
    coverVerticalOpenPicId: varchar('cover_vertical_open_pic_id', { length: 255 }), // 竖版封面图片开放ID
    coverHorizontal: varchar('cover_horizontal', { length: 255 }), // 横版封面图片URL
    coverHorizontalOpenPicId: varchar('cover_horizontal_open_pic_id', { length: 255 }), // 横版封面图片开放ID
    year: int('year'), // 发行年份
    albumStatus: int('album_status').default(1), // 短剧状态: 1-未上映, 2-更新中, 3-已完结
    recommendation: varchar('one_line_recommend', { length: 20 }), // 推荐语
    desp: varchar('desp', { length: 200 }), // 短剧简介
    summary: varchar('summary', { length: 1000 }), // 内容梗概
    tagList: text('tag_list'), // 题材标签，多个用逗号分隔
    categoryId: varchar('category_id', { length: 100 }), // 分类ID 只有一个
    qualification: int('qualification').default(1), // 资质类型 - 1：未报审 - 2：报审通过 - 3：报审不通过 - 4：不建议报审
    duration: int('duration'), // 平均单集时长，单位分钟
    productionOrganisation: varchar('production_organisation', { length: 100 }), // 制作机构
    director: varchar('director', { length: 100 }), // 导演
    producer: varchar('producer', { length: 100 }), // 制片人
    actor: varchar('actor', { length: 100 }), // 演员
    costDistributionUri: varchar('cost_distribution_uri', { length: 255 }), // 成本分配URI
    assuranceUri: varchar('assurance_uri', { length: 255 }), // 承诺书URI
    playletProductionCost: int('playlet_production_cost').default(10), // 制作成本类型 - 10：30万以下 - 20：30～100万 - 30：100万以上
    screenWriter: varchar('screen_writer', { length: 100 }), // 编剧
    recordType: int('record_type').default(1), // 备案类型 - 10：普通备案 - 20：重点备案
    broadcastRecordNumber: varchar('broadcast_record_number', { length: 100 }), // 广电备案号
    licenseNum: varchar('license_num', { length: 100 }), // 许可证号
    registrationNum: varchar('registration_num', { length: 100 }), // 登记号
    ordinaryRecordNum: varchar('ordinary_record_num', { length: 100 }), // 普通备案号
    keyRecordNum: varchar('key_record_num', { length: 100 }), // 重点备案号

    // 小程序业务相关
    introImages: text('intro_images'), // 剧集介绍图片
    introImagesOpenPicId: text('intro_images_open_pic_id'), // 剧集介绍图片开放ID
    originalPrice: decimal('original_price', { precision: 10, scale: 2 }).default('0.00'), // 原价
    discountPrice: decimal('discount_price', { precision: 10, scale: 2 }).default('0.00'), // 优惠价
    freePreview: int('free_preview').default(0), // 是否免费试看: 0-否, 1-是
    currency: varchar('currency', { length: 3 }).default('CNY'), // 货币单位

    douyinAuditStatus: varchar('douyin_audit_status', { length: 20 }).default('pending'), // 抖音审核状态: pending-未审核, reviewing-审核中, approved-审核通过, rejected-审核失败
    douyinPublishStatus: varchar('douyin_publish_status', { length: 20 }).default('draft'), // 抖音发布状态: 0或不填：上线   1：当前短剧下架
    platformAuditStatus: varchar('platform_audit_status', { length: 20 }).default('pending'), // 平台自审状态: pending-未审核, reviewing-审核中, approved-审核通过, rejected-审核失败

    // 审核相关
    version: int('version').default(0), // 抖音送审版本号
    auditSuccessVersion: int('audit_success_version'), // 最后审核通过的版本号
    auditMsg: text('audit_msg'), // 最新审核备注
    scopeList: text('scope_list'), // 最新能力列表（JSON格式）
    lastAuditTime: datetime('last_audit_time'), // 最后审核时间

    // 上线状态相关
    onlineVersion: int('online_version'), // 当前线上版本号
    onlineStatus: int('online_status').default(1), // 上线状态: 0-上线, 1-下线
    onlineTime: datetime('online_time'), // 上线时间

    // 销售统计
    totalSalesAmount: decimal('total_sales_amount', { precision: 12, scale: 2 }).default('0.00'), // 累计销售金额
    totalSalesCount: int('total_sales_count').default(0), // 累计销售数量
    lastDaySales: int('last_day_sales').default(0), // 昨日销量
    last7DaysSales: int('last_7days_sales').default(0), // 近7天销量
    last30DaysSales: int('last_30days_sales').default(0), // 近30天销量
    yearSales: int('year_sales').default(0), // 年度销量
    publishTime: datetime('publish_time'), // 发布时间
    lastSaleTime: datetime('last_sale_time'), // 最后一次销售时间
    isInRecommendPool: int('is_in_recommend_pool').default(0), // 是否在推荐池: 0-否, 1-是
    recommendPoolEnterTime: datetime('recommend_pool_enter_time'), // 进入推荐池时间

    // 创作者信息
    authorId: varchar('author_id', { length: 100 }), // 作者/创作者ID
    createdAt: datetime('created_at').$default(() => new Date()), // 创建时间
    updatedAt: datetime('updated_at')
      .$default(() => new Date())
      .$onUpdate(() => new Date()), // 更新时间
    isDeleted: int('is_deleted').notNull().default(0), // 是否删除: 0-未删除, 1-已删除
  },
  (table) => [
    index('category_idx').on(table.categoryId), // 分类索引
    index('author_idx').on(table.authorId), // 作者索引
    index('publish_time_idx').on(table.publishTime), // 发布时间索引
    index('sales_amount_idx').on(table.totalSalesAmount), // 销售金额索引
    index('recommend_pool_idx').on(table.isInRecommendPool), // 推荐池索引
    index('deleted_idx').on(table.isDeleted), // 删除状态索引
  ],
)

export const dramasRelations = relations(dramas, ({ one, many }) => ({
  author: one(systemUsers, {
    fields: [dramas.authorId],
    references: [systemUsers.id],
  }),
  episodes: many(episodes),
}))
