import { ROLE_DECORATOR_KEY } from '@/constants/system.constants'
import { applyDecorators, SetMetadata, UseGuards } from '@nestjs/common'
import { RolesGuard } from '../guards/roles.guard'
import { RoleEnum } from '@/modules/auth/interfaces/jwt-payload.interface'

/**
 * 管理员角色装饰器
 * 仅允许管理员访问
 */
export const Admin = () => Roles(RoleEnum.ADMIN)

/**
 * 创作者角色装饰器
 * 仅允许创作者访问
 */
export const Creator = () => Roles(RoleEnum.CREATOR)

/**
 * 用户角色装饰器
 * 仅允许普通用户访问
 */
export const User = () => Roles(RoleEnum.USER)

/**
 * 管理员或创作者角色装饰器
 * 允许管理员或创作者访问
 */
export const AdminOrCreator = () => Roles(RoleEnum.ADMIN, RoleEnum.CREATOR)

/**
 * 角色权限装饰器
 * @param roles 允许访问的角色列表
 * @returns 装饰器函数
 */
export const Roles = (...roles: RoleEnum[]) => {
  const decorators: (ClassDecorator | PropertyDecorator | MethodDecorator)[] = [
    SetMetadata(ROLE_DECORATOR_KEY, roles),
    UseGuards(RolesGuard),
  ]

  return applyDecorators(...decorators)
}
