import { ROLE_DECORATOR_KEY } from '@/constants/system.constants'
import { getNestExecutionContextRequest } from '@/transformers/get-req.transformer'
import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common'
import { Reflector } from '@nestjs/core'
import { BizException } from '../exceptions/biz.exception'
import { ErrorCodeEnum } from '@/constants/error-code.constant'

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get<number[]>(ROLE_DECORATOR_KEY, context.getHandler())
    if (!roles) {
      return true
    }
    const request = getNestExecutionContextRequest(context)
    if (!request.user) {
      throw new BizException(ErrorCodeEnum.LOGIN_FAILED)
    }
    const userRole = request.user.role
    const hasRole = roles.includes(userRole)
    if (!hasRole) {
      throw new BizException(ErrorCodeEnum.NO_PERMISSION)
    }
    return hasRole
  }
}
