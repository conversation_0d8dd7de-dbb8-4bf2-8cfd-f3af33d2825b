import { ErrorCodeEnum } from '@/constants/error-code.constant'
import { AuthService } from '@/modules/auth/auth.service'
import { ExpressBizRequest, getNestExecutionContextRequest } from '@/transformers/get-req.transformer'
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common'
import { ClsService } from 'nestjs-cls'
import { DrizzleService } from '../drizzle/database.provider'
import { SystemUserSelectType } from '../drizzle/schema/system-users'
import { BizException } from '../exceptions/biz.exception'

function isJWT(token: string): boolean {
  const parts = token.split('.')
  return (
    parts.length === 3 &&
    /^[a-zA-Z0-9_-]+$/.test(parts[0]) &&
    /^[a-zA-Z0-9_-]+$/.test(parts[1]) &&
    /^[a-zA-Z0-9_-]+$/.test(parts[2])
  )
}

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly authService: AuthService,
    private readonly drizzle: DrizzleService,
    private readonly cls: ClsService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<any> {
    const request = this.getRequest(context)

    const query = request.query as Record<string, any>
    const headers = request.headers
    const Authorization: string = headers.authorization || (headers.Authorization as string) || (query.token as string)

    if (!Authorization) {
      throw new BizException(ErrorCodeEnum.LOGIN_FAILED)
    }

    const jwt = Authorization.replace(/[Bb]earer /, '')

    if (!isJWT(jwt)) {
      throw new BizException(ErrorCodeEnum.LOGIN_FAILED)
    }

    const payload = await this.authService.verifyToken(jwt)
    if (!payload.id) {
      throw new BizException(ErrorCodeEnum.LOGIN_FAILED)
    }

    const user = await this.drizzle.db.query.systemUsers.findFirst({
      where: (systemUsers, { eq }) => eq(systemUsers.id, payload.id),
    })

    if (!user) {
      throw new BizException(ErrorCodeEnum.USER_NOT_FOUND)
    }

    this.attachUserAndToken(request, user, Authorization)
    this.cls.set('user', user)
    return true
  }

  private getRequest(context: ExecutionContext) {
    return getNestExecutionContextRequest(context)
  }

  private attachUserAndToken(request: ExpressBizRequest, user: SystemUserSelectType, token?: string) {
    request.user = user
    request.token = token
  }
}
