import { PipeTransform, Injectable, ArgumentMetadata } from '@nestjs/common'
import { isUndef, isString, isEmptyString, isArray, isPlainObject } from '@/common/utils/misc.util'

@Injectable()
export class CleanEmptyQueryPipe implements PipeTransform {
  transform(value: any, metadata: ArgumentMetadata) {
    if (metadata.type !== 'query' || !isPlainObject(value)) {
      return value
    }

    return this.cleanObject(value)
  }

  private cleanObject(obj: Record<string, any>): Record<string, unknown> {
    const cleaned: Record<string, unknown> = {}

    for (const [key, val] of Object.entries(obj)) {
      const cleanedValue: unknown = this.processValue(val)
      if (!isUndef(cleanedValue)) {
        cleaned[key] = cleanedValue
      }
    }

    return cleaned
  }

  private processValue(value: any): any {
    if (!this.isValidValue(value)) {
      return undefined
    }

    if (isString(value)) {
      return value.trim()
    }

    if (isArray(value)) {
      const cleanedArray = value.map((item) => this.processValue(item)).filter((item) => item !== undefined)

      return cleanedArray.length > 0 ? cleanedArray : undefined
    }

    if (isPlainObject(value)) {
      const cleanedObj = this.cleanObject(value)
      return Object.keys(cleanedObj).length > 0 ? cleanedObj : undefined
    }

    return value
  }

  private isValidValue(value: any): boolean {
    if (isUndef(value)) {
      return false
    }

    if (isEmptyString(value)) {
      return false
    }

    if (isArray(value)) {
      return value.some((item) => this.isValidValue(item))
    }

    if (isPlainObject(value)) {
      return Object.values(value).some((val) => this.isValidValue(val))
    }

    return true
  }
}
