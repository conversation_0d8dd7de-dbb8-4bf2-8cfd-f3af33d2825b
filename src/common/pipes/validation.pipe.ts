import { Injectable, ValidationPipe as NestValidationPipe, ValidationError } from '@nestjs/common'
import { ValidException } from '../exceptions/valid.exception'

@Injectable()
export class ValidationPipe extends NestValidationPipe {
  public createExceptionFactory() {
    return (validationErrors: ValidationError[] = []) => {
      const errors = super.flattenValidationErrors(validationErrors)
      return new ValidException(errors.join(';'))
    }
  }
}
