import { <PERSON><PERSON> } from 'cache-manager'
import { Inject, Injectable, Logger, OnM<PERSON>ule<PERSON><PERSON>roy } from '@nestjs/common'
import { CACHE_MANAGER } from '@nestjs/cache-manager'
import { Keyv } from '@keyv/redis'

export type TCacheKey = string
export type TCacheResult<T> = Promise<T | null | undefined>

@Injectable()
export class CacheService implements OnModuleDestroy {
  private cache!: Cache

  constructor(
    @Inject(CACHE_MANAGER) cache: Cache,
    private readonly logger: Logger,
  ) {
    this.cache = cache
    this.redisClient.on('connect', () => {
      this.logger.log('Redis 连接成功')
    })
  }

  private get redisClient(): Keyv {
    return this.cache.stores.at(0)!
  }

  public get<T>(key: TCacheKey): TCacheResult<T> {
    return this.cache.get(key)
  }

  public set(key: TCacheKey, value: any, milliseconds: number) {
    return this.cache.set(key, value, milliseconds)
  }

  public getClient() {
    return this.redisClient
  }

  async onModuleD<PERSON>roy() {
    await this.redisClient.disconnect()
  }
}
