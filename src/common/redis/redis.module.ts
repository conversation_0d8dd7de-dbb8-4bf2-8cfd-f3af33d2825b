import { CacheModule as NestCacheModule } from '@nestjs/cache-manager'
import { Global, Module } from '@nestjs/common'
import { RedisConfigService } from './redis.config.service'
import { CacheService } from './cache.service'
import { LockService } from './lock.service'

@Global()
@Module({
  imports: [
    NestCacheModule.registerAsync({
      useClass: RedisConfigService,
      inject: [RedisConfigService],
    }),
  ],
  providers: [CacheService, LockService],
  exports: [CacheService, LockService],
})
export class RedisModule {}
