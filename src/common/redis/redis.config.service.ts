import type { CacheModuleOptions, CacheOptionsFactory } from '@nestjs/cache-manager'

import { CACHE_DEFAULT_TTL, REDIS_URL } from '@/app.config'
import { createKeyv } from '@keyv/redis'
import { Injectable } from '@nestjs/common'

@Injectable()
export class RedisConfigService implements CacheOptionsFactory {
  public createCacheOptions(): CacheModuleOptions {
    const uri = REDIS_URL
    const keyv = createKeyv(uri)
    return {
      stores: [keyv],
      ttl: CACHE_DEFAULT_TTL,
    }
  }
}
