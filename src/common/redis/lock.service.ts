import { Injectable, OnModuleD<PERSON>roy } from '@nestjs/common'
import { Redlock } from '@sesamecare-oss/redlock'

import { CacheService } from './cache.service'
import Redis from 'ioredis'
import { REDIS_URL } from '@/app.config'

@Injectable()
export class LockService implements OnModuleDestroy {
  private redlock: Redlock
  private redis: Redis

  constructor(private readonly cache: CacheService) {
    this.redis = new Redis(REDIS_URL)

    this.redlock = new Redlock([this.redis], {
      driftFactor: 0.01,
      retryCount: 10,
      retryDelay: 200,
      retryJitter: 200,
      automaticExtensionThreshold: 500,
    })
  }

  async acquireLock(resource: string | string[], ttl = 1000) {
    return this.redlock.acquire(Array.isArray(resource) ? resource : [resource], ttl)
  }

  async onModuleDestroy() {
    await this.redis.quit()
  }
}
