import { PaginationRequest } from '../requests/pagination.request'

export class PaginationResponse<T> {
  public readonly items: T[]
  public readonly total: number
  public readonly page: number
  public readonly size: number

  constructor(items: T[], total: number, page: number, size: number) {
    this.items = items
    this.total = total
    this.page = Math.max(1, page)
    this.size = Math.max(1, size)
  }

  static from<T>(items: T[], total: number, page: number, size: number) {
    return new PaginationResponse(items, total, page, size)
  }

  static empty<T>() {
    return new PaginationResponse<T>([], 0, 1, 10)
  }

  static fromPagination<T>(items: T[], pagination: PaginationResponse<T>) {
    return new PaginationResponse(items, pagination.total, pagination.page, pagination.size)
  }

  static fromPaginationRequest<T>(items: T[], total: number, pagination: PaginationRequest) {
    return new PaginationResponse(items, total, pagination.page ?? 1, pagination.size ?? 10)
  }

  static fromArray<T>(items: T[]) {
    return new PaginationResponse(items, items.length, 1, items.length)
  }

  static fromArrayWithPagination<T>(items: T[], page: number, size: number) {
    return new PaginationResponse(items, items.length, page, size)
  }

  toJSON() {
    return {
      items: this.items,
      meta: {
        total: this.total,
        page: this.page,
        size: this.size,
      },
    }
  }

  map<R>(fn: (item: T) => R): PaginationResponse<R> {
    return new PaginationResponse(this.items.map(fn), this.total, this.page, this.size)
  }
}
