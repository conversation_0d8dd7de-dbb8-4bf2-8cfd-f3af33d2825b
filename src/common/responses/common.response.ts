export class CommonResponse<T> {
  public readonly code: number
  public readonly message: string
  public readonly data?: T
  constructor(code: number, message: string, data?: T) {
    this.code = code
    this.message = message
    this.data = data
  }

  static from<T>(code: number, message: string, data: T) {
    return new CommonResponse(code, message, data)
  }

  static ok<T>(data?: T) {
    return new CommonResponse(0, 'ok', data)
  }

  static error<T>(code: number, message: string) {
    return new CommonResponse<T>(code, message)
  }

  toJSON() {
    return {
      code: this.code,
      message: this.message,
      data: this.data,
    }
  }
}
