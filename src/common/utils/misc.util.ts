// eslint-disable-next-line @typescript-eslint/unbound-method
export const objectToString = Object.prototype.toString
// eslint-disable-next-line @typescript-eslint/unbound-method
const hasOwnProperty = Object.prototype.hasOwnProperty

export const toTypeString = (value: unknown): string => objectToString.call(value)

export type Fn<T = void> = (...args: any[]) => T

export function isArray(obj: any): obj is any[] {
  return Array.isArray(obj)
}

export const isMap = (val: unknown): val is Map<any, any> => toTypeString(val) === '[object Map]'

export const isSet = (val: unknown): val is Set<any> => toTypeString(val) === '[object Set]'

export const isString = (val: unknown): val is string => typeof val === 'string'

export const isDate = (val: unknown): val is Date => toTypeString(val) === '[object Date]'

export const isFunction = (val: unknown): val is Fn => typeof val === 'function'

export const isSymbol = (val: unknown): val is symbol => typeof val === 'symbol'

export const isObject = (val: unknown): val is Record<any, any> => val !== null && typeof val === 'object'

export function isPromise<T = any>(val: unknown): val is Promise<T> {
  return isObject(val) && isFunction(val.then) && isFunction(val.catch)
}

export const isNumber = (val: any): val is number => typeof val === 'number'

export const isNull = (val: unknown): val is null => toTypeString(val) === '[object Null]'

export const isUndefined = (val: unknown): val is undefined => toTypeString(val) === '[object Undefined]'

export const isRegExp = (val: unknown): val is RegExp => toTypeString(val) === '[object RegExp]'

export const isFile = (val: unknown): val is File => toTypeString(val) === '[object File]'

export const isPlainObject = (val: unknown): val is object => toTypeString(val) === '[object Object]'

export function isUndef(v: unknown) {
  return isNull(v) || isUndefined(v)
}

export function isEmptyString(v: unknown) {
  return isString(v) && v.trim().length === 0
}

export function isEmpty(val: unknown): boolean {
  if (val == null) return true
  if (typeof val === 'object') {
    return !Object.keys(val as Record<string, unknown>).length
  }
  return !val
}

export function isBlank(str: unknown): boolean {
  return !str || (isString(str) && str.trim().length === 0)
}

export function isNotBlank(str: unknown): str is string {
  return !isBlank(str)
}

export function isEmptyStr(str: unknown): boolean {
  return !str || (isString(str) && str.length === 0)
}

export function isNotEmpty(str: unknown): str is string {
  return !isEmptyStr(str)
}

export function defaultIfBlank(str: unknown, defaultStr: string = ''): string {
  return isBlank(str) ? defaultStr : String(str)
}

export function defaultIfEmpty(str: unknown, defaultStr: string = ''): string {
  return isEmptyStr(str) ? defaultStr : String(str)
}

export function parseJsonArray<T = any>(value: unknown, defaultValue: T[] = []): T[] {
  if (!value) return defaultValue
  if (isArray(value)) return value
  if (isString(value)) {
    try {
      const parsed: unknown = JSON.parse(value)
      return isArray(parsed) ? (parsed as T[]) : defaultValue
    } catch {
      return defaultValue
    }
  }
  return defaultValue
}

export const hasOwn = (val: object, key: string | symbol): key is keyof typeof val => hasOwnProperty.call(val, key)

export const noop = () => {}
