import * as bcrypt from 'bcrypt'

const SALT = 10

/**
 * 加密密码
 * @param password 密码
 * @returns 加密之后的密码
 */
export function encode(password: string): string {
  return bcrypt.hashSync(password, SALT)
}

/**
 * 比较密码
 * @param password 密码
 * @param hash 加密后的密码
 * @returns 是否相等
 */
export function compare(password: string, hash: string): boolean {
  return bcrypt.compareSync(password, hash)
}

export default {
  encode,
  compare,
}
