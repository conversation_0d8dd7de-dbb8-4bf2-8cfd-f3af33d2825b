import type { IncomingMessage } from 'node:http'
import type { Request } from 'express'

const IP_HEADERS = ['true-client-ip', 'cf-connecting-ip', 'cf-connecting-ipv6', 'x-forwarded-for', 'x-real-ip'] as const

export const getIp = (request: Request | IncomingMessage): string => {
  const headers = request.headers

  // 从 headers 中查找 IP，支持大小写
  const headerIp = IP_HEADERS.reduce<string | undefined>((foundIp, header) => {
    if (foundIp) return foundIp
    return (headers[header] as string) || (headers[header.toUpperCase()] as string)
  }, undefined)

  // 获取 IP 的优先级顺序
  const ip = headerIp || request?.socket?.remoteAddress || ''

  // 处理多 IP 的情况（如 x-forwarded-for）
  return ip ? ip.split(',')[0].trim() : ''
}
