import { DrizzleDB } from '@/common/drizzle/database.provider'
import { asc, count, desc, SQL, Subquery } from 'drizzle-orm'
import { MySqlColumn, MySqlTable } from 'drizzle-orm/mysql-core'
import { MySqlViewBase } from 'drizzle-orm/mysql-core/view-base'
import { PaginationRequest } from '../requests/pagination.request'
import { hasOwn, isArray, isFunction, isUndef } from './misc.util'
import { withPagination } from '../drizzle/drizzle.helper'

const SORT_FUNCTION = {
  asc,
  desc,
} as const

type OrderSQL = MySqlColumn | SQL | SQL.Aliased

interface PageQueryOption {
  condition?: ((db: DrizzleDB) => SQL | undefined) | SQL
  orderBy?: (() => OrderSQL[] | OrderSQL) | OrderSQL[] | OrderSQL
}

export function pageQuery<TFrom extends MySqlTable | Subquery | MySqlViewBase | SQL, TOrderBy extends string = string>(
  db: DrizzleDB,
  table: TFrom,
  paginationRequest?: PaginationRequest & { orderBy?: TOrderBy },
  option?: PageQueryOption,
) {
  const { condition, orderBy } = option ?? {}

  const baseQuery = db.select().from(table).$dynamic()
  const countQuery = db.select({ count: count() }).from(table)

  let where: SQL | undefined
  if (isFunction(condition)) {
    where = condition(db)
  } else {
    where = condition
  }
  baseQuery.where(where)
  countQuery.where(where)

  if (
    paginationRequest &&
    paginationRequest.orderBy &&
    SORT_FUNCTION[paginationRequest.order] &&
    hasOwn(table, paginationRequest.orderBy)
  ) {
    const { order, orderBy } = paginationRequest
    const sortFn = SORT_FUNCTION[order]
    baseQuery.orderBy(sortFn(table[orderBy]))
    countQuery.orderBy(sortFn(table[orderBy]))
  }

  const finalOrders: OrderSQL[] = []

  if (isFunction(orderBy)) {
    const orders = orderBy()
    const ordersArray = isArray(orders) ? orders : [orders]
    finalOrders.push(...ordersArray)
  } else if (isArray(orderBy)) {
    finalOrders.push(...orderBy)
  } else if (!isUndef(orderBy)) {
    finalOrders.push(orderBy)
  }

  if (finalOrders.length > 0) {
    baseQuery.orderBy(...finalOrders)
    countQuery.orderBy(...finalOrders)
  }

  async function getData(page: number = paginationRequest?.page ?? 1, size: number = paginationRequest?.size ?? 10) {
    const [data, [{ count: total }]] = await Promise.all([withPagination(baseQuery, page, size), countQuery])
    return {
      data,
      total,
    }
  }

  return {
    baseQuery,
    countQuery,
    getData,
  }
}

export type PageQuery = ReturnType<typeof pageQuery>
