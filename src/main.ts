import { NestFactory } from '@nestjs/core'
import { NestExpressApplication } from '@nestjs/platform-express'
import { WinstonModule } from 'nest-winston'
import { APP_PORT } from '@/app.config'
import { AppModule } from '@/app.module'
import { logger } from '@/global/logger.global'
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger'
import { VersioningType } from '@nestjs/common'
import { ValidationPipe } from '@/common/pipes/validation.pipe'
import { CleanEmptyQueryPipe } from '@/common/pipes/clean-empty-query.pipe'

function setupSwagger(app: NestExpressApplication) {
  const config = new DocumentBuilder()
    .setTitle('UGC 小程序 API')
    .setDescription('UGC 小程序 API')
    .setVersion('1.0')
    .build()
  const documentFactory = () => SwaggerModule.createDocument(app, config)
  SwaggerModule.setup('swagger', app, documentFactory)
}

function setupFeature(app: NestExpressApplication) {
  app.setGlobalPrefix('api')
  app.enableVersioning({
    defaultVersion: '1',
    type: VersioningType.URI,
  })
}

export async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    cors: true,
    logger: WinstonModule.createLogger({ instance: logger }),
  })

  setupSwagger(app)
  setupFeature(app)

  app.useGlobalPipes(
    new CleanEmptyQueryPipe(),
    new ValidationPipe({
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  )
  await app.listen(APP_PORT)
}

void bootstrap()
