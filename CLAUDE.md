# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `pnpm start:dev` - Start development server with hot reload
- `pnpm build` - Build production bundle using Webpack
- `pnpm start:prod` - Start production server

### Database (Drizzle ORM)
- `pnpm db:push:dev` - Push schema changes to development database
- `pnpm db:migrate:dev` - Apply migrations to development database
- `pnpm db:studio:dev` - Open Drizzle Studio for development database
- `pnpm db:generate:dev` - Generate new migration from schema changes
- `pnpm db:push:prod` - Push schema changes to production database
- `pnpm db:studio:prod` - Open Drizzle Studio for production database

### Code Quality
- `pnpm lint` - Run ESLint with auto-fix
- `pnpm format` - Format code with Prettier
- `pnpm test` - Run unit tests
- `pnpm test:e2e` - Run end-to-end tests
- `pnpm test:watch` - Run tests in watch mode
- `pnpm test:cov` - Run tests with coverage report

### Development Environment
- Use `docker compose -f compose-dev.yaml up -d` to start MySQL and Redis containers
- Default database: `mysql://root:123456@localhost:3306/ugc`
- Default Redis: `redis://localhost:6379`

## Architecture Overview

### Tech Stack
- **Framework**: NestJS with Express
- **Database**: MySQL with Drizzle ORM
- **Cache**: Redis with cache-manager
- **Authentication**: JWT with Passport
- **File Storage**: Volcengine TOS (Tencent Object Storage)
- **External APIs**: 抖音 (Douyin) Mini Program API

### Module Structure
- **`src/modules/auth/`** - JWT authentication and SMS verification
- **`src/modules/douyin/`** - 抖音小程序相关功能 (categories, dramas, rankings)
- **`src/modules/system/`** - 后台管理系统 (users, configs, content management)
- **`src/shared/tos/`** - 火山引擎对象存储服务
- **`src/shared/tasks/`** - 定时任务调度

### Database Schema
Located in `src/common/drizzle/schema/`:
- **Core entities**: dramas, episodes, categories, genres, comments
- **System entities**: system_users, system_configs, system_dict
- **Business entities**: orders, ranking_caches, files
- All entities include `createdAt`, `updatedAt`, `isDeleted` for soft deletion

### Common Patterns
- **Base classes**: All response DTOs with timestamps extend `BaseResponse` from `src/common/responses/base.response.ts`
- **Validation**: Use `ValidationPipe` with class-validator decorators
- **Path alias**: `@/*` maps to `src/*`
- **Global exception handling**: `AllExceptionsFilter` catches all errors
- **Swagger**: API documentation available at `/swagger`

## Code Style Requirements

### Prettier Configuration
```json
{
  "arrowParens": "always",
  "bracketSameLine": true,
  "bracketSpacing": true,
  "printWidth": 120,
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "all"
}
```

### Development Guidelines
- Avoid code duplication - extract common types when used multiple times
- All response DTOs with `createdAt`, `updatedAt`, `isDeleted` must extend `BaseResponse`
- Use path alias `@/` for imports within src directory
- Follow NestJS module organization (controllers, services, requests, responses)

## Environment Configuration

### Development
- Database: `mysql://root:123456@localhost:3306/ugc`
- Redis: `redis://localhost:6379`
- App runs on port 8000

### Production
- Uses environment-specific database and Redis URLs
- Configured for 火山引擎 (Volcengine) cloud services
- Docker deployment with multi-stage build

## Testing
- Unit tests use Jest
- E2E tests configuration in `test/jest-e2e.json`
- Test files should end with `.spec.ts`
- Coverage reports generated in `coverage/` directory