services:
  app:
    container_name: ugc-backend
    build:
      context: .
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"
    volumes:
      - $PWD/logs:/opt/application/logs
    env_file:
      - .env
    environment:
      TZ: Asia/Shanghai
      NODE_ENV: production
      DATABASE_URL: mysql://root:123456@mysql:3306/ugc
    depends_on:
      - mysql
      - redis

  mysql:
    container_name: mysql
    image: mysql:latest
    restart: always
    command:
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --max_allowed_packet=128M
      --sql_mode=STRICT_TRANS_TABLES,NO_ZERO_IN_DATE,NO_ZERO_DATE,ERROR_FOR_DIVISION_BY_ZERO,NO_ENGINE_SUBSTITUTION
    ports:
      - "3306:3306"
    volumes:
      - $PWD/docker/db/var/lib/mysql:/var/lib/mysql
      - $PWD/docker/db/mysqlBackup:/data/mysqlBackup
    env_file:
      - .env
    environment:
      TZ: Asia/Shanghai
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: ugc
      MYSQL_ROOT_HOST: '%'
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p123456"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    container_name: redis
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - $PWD/docker/redis/data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

networks:
  default:
    name: ugc-network
    driver: bridge
