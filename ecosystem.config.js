const { cpus } = require('node:os')
const { execSync } = require('node:child_process')
const nodePath = execSync(`npm root --quiet -g`, { encoding: 'utf-8' }).split('\n')[0]

const cpuLen = cpus().length
module.exports = {
  apps: [
    {
      name: 'ugc-backend',
      script: './dist/main.js',
      autorestart: true,
      exec_mode: 'cluster',
      watch: false,
      instances: cpuLen,
      max_memory_restart: '520M',
      env: {
        NODE_ENV: 'production',
        NODE_PATH: nodePath,
      },
    },
  ],
}
