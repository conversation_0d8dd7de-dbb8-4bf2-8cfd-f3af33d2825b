{"version": "5", "dialect": "mysql", "id": "09d3bd7d-3d05-47bc-991d-3be8029bb5dc", "prevId": "4c930dfd-aa74-4a26-aa6c-4c589dbeb16e", "tables": {"categories": {"name": "categories", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_index": {"name": "order_index", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"categories_id": {"name": "categories_id", "columns": ["id"]}}, "uniqueConstraints": {"categories_name_unique": {"name": "categories_name_unique", "columns": ["name"]}}, "checkConstraint": {}}, "comments": {"name": "comments", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "dramaId": {"name": "dramaId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "parent_id": {"name": "parent_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "reply_to_user_id": {"name": "reply_to_user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "like_count": {"name": "like_count", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "reply_count": {"name": "reply_count", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "status": {"name": "status", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "is_featured": {"name": "is_featured", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {"comments_userId_system_user_id_fk": {"name": "comments_userId_system_user_id_fk", "tableFrom": "comments", "tableTo": "system_user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "comments_dramaId_dramas_id_fk": {"name": "comments_dramaId_dramas_id_fk", "tableFrom": "comments", "tableTo": "dramas", "columnsFrom": ["dramaId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"comments_id": {"name": "comments_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "creator_application": {"name": "creator_application", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "real_name": {"name": "real_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "id_card": {"name": "id_card", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "reason": {"name": "reason", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true, "autoincrement": false}, "portfolio_url": {"name": "portfolio_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "extra_info": {"name": "extra_info", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "audit_remark": {"name": "audit_remark", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "audit_by": {"name": "audit_by", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "audit_at": {"name": "audit_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"creator_application_id": {"name": "creator_application_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "dramas": {"name": "dramas", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "album_id": {"name": "album_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "seq_num": {"name": "seq_num", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "cover_vertical": {"name": "cover_vertical", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_vertical_open_pic_id": {"name": "cover_vertical_open_pic_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_horizontal": {"name": "cover_horizontal", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_horizontal_open_pic_id": {"name": "cover_horizontal_open_pic_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "year": {"name": "year", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "album_status": {"name": "album_status", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "one_line_recommend": {"name": "one_line_recommend", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "desp": {"name": "desp", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false, "autoincrement": false}, "tag_list": {"name": "tag_list", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "qualification": {"name": "qualification", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "duration": {"name": "duration", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false}, "production_organisation": {"name": "production_organisation", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "director": {"name": "director", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "producer": {"name": "producer", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "actor": {"name": "actor", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cost_distribution_uri": {"name": "cost_distribution_uri", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "assurance_uri": {"name": "assurance_uri", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "playlet_production_cost": {"name": "playlet_production_cost", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 10}, "screen_writer": {"name": "screen_writer", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "record_type": {"name": "record_type", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "broadcast_record_number": {"name": "broadcast_record_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "license_num": {"name": "license_num", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "registration_num": {"name": "registration_num", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "ordinary_record_num": {"name": "ordinary_record_num", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "key_record_num": {"name": "key_record_num", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "intro_images": {"name": "intro_images", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "intro_images_open_pic_id": {"name": "intro_images_open_pic_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "original_price": {"name": "original_price", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "discount_price": {"name": "discount_price", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "free_preview": {"name": "free_preview", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'CNY'"}, "douyin_audit_status": {"name": "douyin_audit_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "douyin_publish_status": {"name": "douyin_publish_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'draft'"}, "platform_audit_status": {"name": "platform_audit_status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'pending'"}, "total_sales_amount": {"name": "total_sales_amount", "type": "decimal(12,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "total_sales_count": {"name": "total_sales_count", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "last_day_sales": {"name": "last_day_sales", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "last_7days_sales": {"name": "last_7days_sales", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "last_30days_sales": {"name": "last_30days_sales", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "year_sales": {"name": "year_sales", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "publish_time": {"name": "publish_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_sale_time": {"name": "last_sale_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_in_recommend_pool": {"name": "is_in_recommend_pool", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "recommend_pool_enter_time": {"name": "recommend_pool_enter_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {"category_idx": {"name": "category_idx", "columns": ["category_id"], "isUnique": false}, "author_idx": {"name": "author_idx", "columns": ["author_id"], "isUnique": false}, "publish_time_idx": {"name": "publish_time_idx", "columns": ["publish_time"], "isUnique": false}, "sales_amount_idx": {"name": "sales_amount_idx", "columns": ["total_sales_amount"], "isUnique": false}, "recommend_pool_idx": {"name": "recommend_pool_idx", "columns": ["is_in_recommend_pool"], "isUnique": false}, "deleted_idx": {"name": "deleted_idx", "columns": ["is_deleted"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"dramas_id": {"name": "dramas_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "episode": {"name": "episode", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "dramaId": {"name": "dramaId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "seq": {"name": "seq", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "cover": {"name": "cover", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_open_pic_id": {"name": "cover_open_pic_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "open_video_id": {"name": "open_video_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "video_url": {"name": "video_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "video_orientation": {"name": "video_orientation", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "douyin_publish_status": {"name": "douyin_publish_status", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "file_size": {"name": "file_size", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_free": {"name": "is_free", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "price": {"name": "price", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "view_count": {"name": "view_count", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "playback_count": {"name": "playback_count", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"episode_id": {"name": "episode_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "genres": {"name": "genres", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_index": {"name": "order_index", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"genres_id": {"name": "genres_id", "columns": ["id"]}}, "uniqueConstraints": {"genres_name_unique": {"name": "genres_name_unique", "columns": ["name"]}}, "checkConstraint": {}}, "orders": {"name": "orders", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "userId": {"name": "userId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "dramaId": {"name": "dramaId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "episodeId": {"name": "episodeId", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_amount": {"name": "order_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "discount_amount": {"name": "discount_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "final_amount": {"name": "final_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'0.00'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'pending'"}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_platform": {"name": "payment_platform", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_currency": {"name": "payment_currency", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'CNY'"}, "client_platform": {"name": "client_platform", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'ANDROID'"}, "diamond_amount": {"name": "diamond_amount", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "payment_time": {"name": "payment_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "refund_status": {"name": "refund_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'none'"}, "refund_amount": {"name": "refund_amount", "type": "decimal(10,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "refund_time": {"name": "refund_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "refund_reason": {"name": "refund_reason", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_source": {"name": "order_source", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "autoincrement": false}, "order_type": {"name": "order_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'drama'"}, "order_note": {"name": "order_note", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "platform_share": {"name": "platform_share", "type": "decimal(5,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "creator_share": {"name": "creator_share", "type": "decimal(5,2)", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "'0.00'"}, "expire_time": {"name": "expire_time", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {"orders_dramaId_dramas_id_fk": {"name": "orders_dramaId_dramas_id_fk", "tableFrom": "orders", "tableTo": "dramas", "columnsFrom": ["dramaId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "orders_episodeId_episode_id_fk": {"name": "orders_episodeId_episode_id_fk", "tableFrom": "orders", "tableTo": "episode", "columnsFrom": ["episodeId"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {"orders_id": {"name": "orders_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "ranking_cache": {"name": "ranking_cache", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "rank_type": {"name": "rank_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "drama_id": {"name": "drama_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "rank": {"name": "rank", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false}, "score": {"name": "score", "type": "decimal(10,2)", "primaryKey": false, "notNull": true, "autoincrement": false}, "cache_date": {"name": "cache_date", "type": "datetime", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"ranking_cache_id": {"name": "ranking_cache_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "system_config": {"name": "system_config", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "config_key": {"name": "config_key", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "config_value": {"name": "config_value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"system_config_id": {"name": "system_config_id", "columns": ["id"]}}, "uniqueConstraints": {"system_config_config_key_unique": {"name": "system_config_config_key_unique", "columns": ["config_key"]}}, "checkConstraint": {}}, "system_dict": {"name": "system_dict", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "order": {"name": "order", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "status": {"name": "status", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 1}, "remark": {"name": "remark", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {"idx_type": {"name": "idx_type", "columns": ["type"], "isUnique": false}}, "foreignKeys": {}, "compositePrimaryKeys": {"system_dict_id": {"name": "system_dict_id", "columns": ["id"]}}, "uniqueConstraints": {"system_dict_type_key_unique": {"name": "system_dict_type_key_unique", "columns": ["type", "key"]}}, "checkConstraint": {}}, "system_user": {"name": "system_user", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "nickname": {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "bio": {"name": "bio", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false, "autoincrement": false}, "open_id": {"name": "open_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "union_id": {"name": "union_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "session_key": {"name": "session_key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "anonymous_openid": {"name": "anonymous_openid", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 2}, "status": {"name": "status", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1}, "last_login_at": {"name": "last_login_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "last_login_ip": {"name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "has_creator_approval": {"name": "has_creator_approval", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "is_deleted": {"name": "is_deleted", "type": "int", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"system_user_id": {"name": "system_user_id", "columns": ["id"]}}, "uniqueConstraints": {"system_user_username_unique": {"name": "system_user_username_unique", "columns": ["username"]}, "system_user_email_unique": {"name": "system_user_email_unique", "columns": ["email"]}, "system_user_phone_unique": {"name": "system_user_phone_unique", "columns": ["phone"]}}, "checkConstraint": {}}, "user_favorites": {"name": "user_favorites", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "drama_id": {"name": "drama_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"user_favorites_id": {"name": "user_favorites_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}, "watch_history": {"name": "watch_history", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "drama_id": {"name": "drama_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "episode_id": {"name": "episode_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true, "autoincrement": false}, "watch_progress": {"name": "watch_progress", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "watch_duration": {"name": "watch_duration", "type": "int", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "last_watch_at": {"name": "last_watch_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "datetime", "primaryKey": false, "notNull": false, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"watch_history_id": {"name": "watch_history_id", "columns": ["id"]}}, "uniqueConstraints": {}, "checkConstraint": {}}}, "views": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {"\"episode\".\"episode_number\"": "\"episode\".\"seq\""}}, "internal": {"tables": {}, "indexes": {}}}