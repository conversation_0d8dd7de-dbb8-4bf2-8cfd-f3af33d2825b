CREATE TABLE `categories` (
	`id` varchar(100) NOT NULL,
	`name` varchar(50) NOT NULL,
	`description` varchar(255),
	`order_index` int NOT NULL,
	`created_at` datetime,
	CONSTRAINT `categories_id` PRIMARY KEY(`id`),
	CONSTRAINT `categories_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `comments` (
	`id` varchar(100) NOT NULL,
	`userId` varchar(100) NOT NULL,
	`dramaId` varchar(100) NOT NULL,
	`content` text NOT NULL,
	`parent_id` varchar(100),
	`reply_to_user_id` varchar(100),
	`like_count` int DEFAULT 0,
	`reply_count` int DEFAULT 0,
	`status` int NOT NULL DEFAULT 1,
	`is_featured` int NOT NULL DEFAULT 0,
	`created_at` datetime,
	`updated_at` datetime,
	`is_deleted` int NOT NULL DEFAULT 0,
	CONSTRAINT `comments_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `creator_application` (
	`id` varchar(100) NOT NULL,
	`user_id` varchar(100) NOT NULL,
	`real_name` varchar(50) NOT NULL,
	`id_card` varchar(50) NOT NULL,
	`phone` varchar(20) NOT NULL,
	`email` varchar(100),
	`reason` varchar(500) NOT NULL,
	`portfolio_url` varchar(255),
	`extra_info` varchar(500),
	`attachments` text,
	`status` int NOT NULL DEFAULT 0,
	`audit_remark` varchar(255),
	`audit_by` varchar(100),
	`audit_at` datetime,
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `creator_application_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `dramas` (
	`id` varchar(100) NOT NULL,
	`album_id` varchar(100),
	`title` varchar(100) NOT NULL,
	`seq_num` int DEFAULT 0,
	`cover_vertical` varchar(255),
	`cover_vertical_open_pic_id` varchar(255),
	`cover_horizontal` varchar(255),
	`cover_horizontal_open_pic_id` varchar(255),
	`year` int,
	`album_status` int DEFAULT 1,
	`one_line_recommend` varchar(20),
	`desp` varchar(200),
	`tag_list` text,
	`category_id` varchar(100),
	`qualification` int DEFAULT 1,
	`duration` int,
	`production_organisation` varchar(100),
	`director` varchar(100),
	`producer` varchar(100),
	`actor` varchar(100),
	`cost_distribution_uri` varchar(255),
	`assurance_uri` varchar(255),
	`playlet_production_cost` int DEFAULT 10,
	`screen_writer` varchar(100),
	`record_type` int DEFAULT 1,
	`broadcast_record_number` varchar(100),
	`license_num` varchar(100),
	`registration_num` varchar(100),
	`ordinary_record_num` varchar(100),
	`key_record_num` varchar(100),
	`intro_images` text,
	`intro_images_open_pic_id` text,
	`original_price` decimal(10,2) DEFAULT '0.00',
	`discount_price` decimal(10,2) DEFAULT '0.00',
	`free_preview` int DEFAULT 0,
	`currency` varchar(3) DEFAULT 'CNY',
	`douyin_audit_status` varchar(20) DEFAULT 'pending',
	`douyin_publish_status` varchar(20) DEFAULT 'draft',
	`platform_audit_status` varchar(20) DEFAULT 'pending',
	`total_sales_amount` decimal(12,2) DEFAULT '0.00',
	`total_sales_count` int DEFAULT 0,
	`last_day_sales` int DEFAULT 0,
	`last_7days_sales` int DEFAULT 0,
	`last_30days_sales` int DEFAULT 0,
	`year_sales` int DEFAULT 0,
	`publish_time` datetime,
	`last_sale_time` datetime,
	`is_in_recommend_pool` int DEFAULT 0,
	`recommend_pool_enter_time` datetime,
	`author_id` varchar(100),
	`created_at` datetime,
	`updated_at` datetime,
	`is_deleted` int NOT NULL DEFAULT 0,
	CONSTRAINT `dramas_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `episode` (
	`id` varchar(100) NOT NULL,
	`dramaId` varchar(100) NOT NULL,
	`episode_number` int NOT NULL,
	`title` varchar(100) NOT NULL,
	`cover` varchar(255),
	`video_url` varchar(255) NOT NULL,
	`video_orientation` int DEFAULT 1,
	`douyin_publish_status` int DEFAULT 0,
	`description` text,
	`duration` varchar(20),
	`file_size` varchar(50),
	`is_free` int DEFAULT 0,
	`price` decimal(10,2) DEFAULT '0.00',
	`view_count` int DEFAULT 0,
	`playback_count` int DEFAULT 0,
	`created_at` datetime,
	`updated_at` datetime,
	`is_deleted` int NOT NULL DEFAULT 0,
	CONSTRAINT `episode_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `genres` (
	`id` varchar(100) NOT NULL,
	`name` varchar(50) NOT NULL,
	`description` varchar(255),
	`order_index` int NOT NULL,
	`status` int NOT NULL DEFAULT 1,
	`created_at` datetime,
	`updated_at` datetime,
	`is_deleted` int NOT NULL DEFAULT 0,
	CONSTRAINT `genres_id` PRIMARY KEY(`id`),
	CONSTRAINT `genres_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `orders` (
	`id` varchar(100) NOT NULL,
	`order_no` varchar(50) NOT NULL,
	`userId` varchar(100) NOT NULL,
	`dramaId` varchar(100),
	`episodeId` varchar(100),
	`order_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`discount_amount` decimal(10,2) DEFAULT '0.00',
	`final_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
	`status` varchar(50) NOT NULL DEFAULT 'pending',
	`payment_method` varchar(50),
	`payment_platform` varchar(50),
	`payment_currency` varchar(20) NOT NULL DEFAULT 'CNY',
	`client_platform` varchar(20) NOT NULL DEFAULT 'ANDROID',
	`diamond_amount` int DEFAULT 0,
	`transaction_id` varchar(100),
	`payment_time` datetime,
	`refund_status` varchar(50) DEFAULT 'none',
	`refund_amount` decimal(10,2) DEFAULT '0.00',
	`refund_time` datetime,
	`refund_reason` text,
	`order_source` varchar(50),
	`order_type` varchar(50) NOT NULL DEFAULT 'drama',
	`order_note` text,
	`platform_share` decimal(5,2) DEFAULT '0.00',
	`creator_share` decimal(5,2) DEFAULT '0.00',
	`expire_time` datetime,
	`created_at` datetime,
	`updated_at` datetime,
	`is_deleted` int NOT NULL DEFAULT 0,
	CONSTRAINT `orders_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `ranking_cache` (
	`id` varchar(100) NOT NULL,
	`rank_type` varchar(50) NOT NULL,
	`category_id` varchar(100),
	`drama_id` varchar(100) NOT NULL,
	`rank` int NOT NULL,
	`score` decimal(10,2) NOT NULL,
	`cache_date` datetime NOT NULL,
	`created_at` datetime,
	CONSTRAINT `ranking_cache_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `system_config` (
	`id` varchar(100) NOT NULL,
	`config_key` varchar(50) NOT NULL,
	`config_value` varchar(255) NOT NULL,
	`description` varchar(255),
	`updated_by` varchar(100),
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `system_config_id` PRIMARY KEY(`id`),
	CONSTRAINT `system_config_config_key_unique` UNIQUE(`config_key`)
);
--> statement-breakpoint
CREATE TABLE `system_dict` (
	`id` varchar(100) NOT NULL,
	`type` varchar(50) NOT NULL,
	`key` varchar(50) NOT NULL,
	`value` varchar(255) NOT NULL,
	`order` int DEFAULT 0,
	`status` int DEFAULT 1,
	`remark` text,
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `system_dict_id` PRIMARY KEY(`id`),
	CONSTRAINT `system_dict_type_key_unique` UNIQUE(`type`,`key`)
);
--> statement-breakpoint
CREATE TABLE `system_user` (
	`id` varchar(100) NOT NULL,
	`username` varchar(255) NOT NULL,
	`password` varchar(255),
	`nickname` varchar(255) NOT NULL,
	`email` varchar(100),
	`phone` varchar(20),
	`avatar` varchar(255),
	`bio` varchar(500),
	`open_id` varchar(100),
	`union_id` varchar(100),
	`session_key` varchar(100),
	`anonymous_openid` varchar(100),
	`role` int NOT NULL DEFAULT 2,
	`status` int NOT NULL DEFAULT 1,
	`last_login_at` datetime,
	`last_login_ip` varchar(255),
	`created_at` datetime,
	`has_creator_approval` int NOT NULL DEFAULT 0,
	`updated_at` datetime,
	`is_deleted` int NOT NULL DEFAULT 0,
	CONSTRAINT `system_user_id` PRIMARY KEY(`id`),
	CONSTRAINT `system_user_username_unique` UNIQUE(`username`),
	CONSTRAINT `system_user_email_unique` UNIQUE(`email`),
	CONSTRAINT `system_user_phone_unique` UNIQUE(`phone`)
);
--> statement-breakpoint
CREATE TABLE `user_favorites` (
	`id` varchar(100) NOT NULL,
	`user_id` varchar(100) NOT NULL,
	`drama_id` varchar(100) NOT NULL,
	`created_at` datetime,
	CONSTRAINT `user_favorites_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `watch_history` (
	`id` varchar(100) NOT NULL,
	`user_id` varchar(100) NOT NULL,
	`drama_id` varchar(100) NOT NULL,
	`episode_id` varchar(100) NOT NULL,
	`watch_progress` int DEFAULT 0,
	`watch_duration` int DEFAULT 0,
	`last_watch_at` datetime,
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `watch_history_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
ALTER TABLE `comments` ADD CONSTRAINT `comments_userId_system_user_id_fk` FOREIGN KEY (`userId`) REFERENCES `system_user`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `comments` ADD CONSTRAINT `comments_dramaId_dramas_id_fk` FOREIGN KEY (`dramaId`) REFERENCES `dramas`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `orders` ADD CONSTRAINT `orders_dramaId_dramas_id_fk` FOREIGN KEY (`dramaId`) REFERENCES `dramas`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `orders` ADD CONSTRAINT `orders_episodeId_episode_id_fk` FOREIGN KEY (`episodeId`) REFERENCES `episode`(`id`) ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX `category_idx` ON `dramas` (`category_id`);--> statement-breakpoint
CREATE INDEX `author_idx` ON `dramas` (`author_id`);--> statement-breakpoint
CREATE INDEX `publish_time_idx` ON `dramas` (`publish_time`);--> statement-breakpoint
CREATE INDEX `sales_amount_idx` ON `dramas` (`total_sales_amount`);--> statement-breakpoint
CREATE INDEX `recommend_pool_idx` ON `dramas` (`is_in_recommend_pool`);--> statement-breakpoint
CREATE INDEX `deleted_idx` ON `dramas` (`is_deleted`);--> statement-breakpoint
CREATE INDEX `idx_type` ON `system_dict` (`type`);