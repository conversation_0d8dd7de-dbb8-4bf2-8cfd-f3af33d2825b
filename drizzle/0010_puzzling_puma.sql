CREATE TABLE `comment_likes` (
	`id` varchar(100) NOT NULL,
	`user_id` varchar(100) NOT NULL,
	`comment_id` varchar(100) NOT NULL,
	`status` int NOT NULL DEFAULT 1,
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `comment_likes_id` PRIMARY KEY(`id`),
	CONSTRAINT `uk_user_comment` UNIQUE(`user_id`,`comment_id`)
);
--> statement-breakpoint
ALTER TABLE `comments` MODIFY COLUMN `episodeId` varchar(100) NOT NULL;--> statement-breakpoint
ALTER TABLE `comment_likes` ADD CONSTRAINT `comment_likes_user_id_system_user_id_fk` FOREIGN KEY (`user_id`) REFERENCES `system_user`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `comment_likes` ADD CONSTRAINT `comment_likes_comment_id_comments_id_fk` FOREI<PERSON><PERSON>EY (`comment_id`) REFERENCES `comments`(`id`) ON DELETE no action ON UPDATE no action;