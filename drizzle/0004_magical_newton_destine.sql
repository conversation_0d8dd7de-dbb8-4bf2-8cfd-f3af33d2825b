CREATE TABLE `douyin_audit_records` (
	`id` varchar(100) NOT NULL,
	`callback_type` varchar(50) NOT NULL,
	`album_id` varchar(100),
	`episode_id` varchar(100),
	`version` int,
	`audit_status` int,
	`scope_list` json,
	`audit_msg` text,
	`raw_callback_data` json NOT NULL,
	`callback_time` datetime,
	`processed` boolean DEFAULT false,
	`created_at` datetime,
	`updated_at` datetime,
	CONSTRAINT `douyin_audit_records_id` PRIMARY KEY(`id`),
	CONSTRAINT `uk_album_audit` UNIQUE(`album_id`,`version`,`audit_status`,`callback_type`),
	CONSTRAINT `uk_episode_audit` UNIQUE(`episode_id`,`version`,`audit_status`,`callback_type`)
);
--> statement-breakpoint
ALTER TABLE `dramas` ADD `version` int DEFAULT 0;--> statement-breakpoint
ALTER TABLE `dramas` ADD `audit_msg` text;--> statement-breakpoint
ALTER TABLE `dramas` ADD `scope_list` text;--> statement-breakpoint
ALTER TABLE `dramas` ADD `last_audit_time` datetime;--> statement-breakpoint
CREATE INDEX `callback_type_idx` ON `douyin_audit_records` (`callback_type`);--> statement-breakpoint
CREATE INDEX `album_id_idx` ON `douyin_audit_records` (`album_id`);--> statement-breakpoint
CREATE INDEX `episode_id_idx` ON `douyin_audit_records` (`episode_id`);--> statement-breakpoint
CREATE INDEX `processed_idx` ON `douyin_audit_records` (`processed`);--> statement-breakpoint
CREATE INDEX `callback_time_idx` ON `douyin_audit_records` (`callback_time`);