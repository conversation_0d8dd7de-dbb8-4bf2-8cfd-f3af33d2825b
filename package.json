{"name": "ugc-backend", "version": "0.0.1", "private": true, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "db:push:prod": "drizzle-kit push --config=drizzle-prod.config.ts", "db:studio:prod": "drizzle-kit studio --config=drizzle-prod.config.ts", "db:migrate:prod": "drizzle-kit migrate --config=drizzle-prod.config.ts", "db:generate:dev": "drizzle-kit generate --config=drizzle-dev.config.ts", "db:migrate:dev": "drizzle-kit migrate --config=drizzle-dev.config.ts", "db:push:dev": "drizzle-kit push --config=drizzle-dev.config.ts", "db:studio:dev": "drizzle-kit studio --config=drizzle-dev.config.ts", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prod": "cross-env NODE_ENV=production pm2 start ecosystem.config.js", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "preinstall": "npx only-allow pnpm", "commit": "cz", "prepare": "npm-run-all --continue-on-error prepare:husky", "prepare:husky": "husky", "seed": "tsx scripts/seed.ts"}, "dependencies": {"@keyv/redis": "^4.3.3", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.17", "@nestjs/core": "^11.0.17", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.17", "@nestjs/platform-socket.io": "^11.0.17", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.3", "@nestjs/throttler": "^6.4.0", "@nestjs/websockets": "^11.0.17", "@open-dy/open_api_credential": "^1.0.0", "@open-dy/open_api_sdk": "^1.1.0", "@sesamecare-oss/redlock": "^1.4.0", "@volcengine/openapi": "^1.30.0", "@volcengine/tos-sdk": "^2.7.4", "axios": "^1.8.4", "bcrypt": "^5.1.1", "bullmq": "^5.56.5", "cache-manager": "^6.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dayjs": "^1.11.13", "decimal.js": "^10.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.41.0", "ioredis": "^5.6.1", "json-bignumber": "^1.1.1", "ksuid": "^3.0.0", "lodash-unified": "^1.0.3", "mysql2": "^3.14.0", "nest-winston": "^1.10.2", "nestjs-cls": "^5.4.2", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.24.0", "@nestjs/cli": "^11.0.6", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.17", "@swc/cli": "^0.6.0", "@swc/core": "^1.11.21", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/node": "^22.14.1", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.3", "cross-env": "^7.0.3", "cz-git": "^1.12.0", "drizzle-kit": "^0.30.6", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "globals": "^16.0.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.1.2", "npm-run-all2": "^8.0.4", "pm2": "^6.0.8", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3", "typescript-eslint": "^8.29.1"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}}, "pnpm": {"onlyBuiltDependencies": ["@nestjs/core", "@scarf/scarf", "@swc/core", "bcrypt", "esbuild", "protobufjs", "puppeteer", "tos-crc64-js"]}}