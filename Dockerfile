# 构建阶段
FROM node:22-alpine AS builder
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache python3 make g++ linux-headers
WORKDIR /opt/application/

# 安装 pnpm 并配置镜像源
RUN npm install -g pnpm 
# 复制 package.json 和 lock 文件
COPY package*.json ./
COPY pnpm-lock.yaml ./
COPY .npmrc ./

ENV NODE_ENV=production

# 安装依赖
RUN pnpm install --registry=https://registry.npmmirror.com

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build

# 运行阶段
FROM node:22-alpine AS runner
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk add --no-cache python3 make g++ linux-headers
WORKDIR /opt/application/

# 安装 pm2
RUN npm install -g pm2

# 安装 pnpm 并配置镜像源
RUN npm install -g pnpm
# 复制 package.json 和 lock 文件
COPY package*.json ./
COPY pnpm-lock.yaml ./
COPY .npmrc ./
COPY ecosystem.config.js ./
COPY run.sh ./
USER root

# 设置环境变量
ENV NODE_ENV=production

# 仅安装生产依赖
RUN pnpm install --registry=https://registry.npmmirror.com

# 从构建阶段复制编译后的代码
COPY --from=builder /opt/application/dist ./dist

RUN chmod -R 777 /opt/application/run.sh

# 暴露端口
EXPOSE 8000

# 启动应用
CMD /opt/application/run.sh
