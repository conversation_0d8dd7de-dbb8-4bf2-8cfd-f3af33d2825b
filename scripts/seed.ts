import * as schema from '@/common/drizzle/schema'
import { Role } from '@/modules/auth/interfaces/jwt-payload.interface'
import { drizzle } from 'drizzle-orm/mysql2'
import { createPool } from 'mysql2/promise'

const conn = createPool({
  uri: 'mysql://root:123456@localhost:3306/ugc',
  // uri: 'mysql://ugc_backend:sPuWWGhnbuuLN8M@*************:3306/ugc',
})
export const db = drizzle(conn, { schema, mode: 'default' })

async function init() {
  await db.insert(schema.systemUsers).values({
    username: 'admin',
    password: 'admin',
    phone: '15573130221',
    nickname: 'admin',
    avatar: 'https://a.520gexing.com/uploads/allimg/2019081518/wt0h1f4hnao.jpg',
    role: Role.ADMIN,
  })
  await initGenres()
}

async function initGenres() {
  const genresList = [
    { id: 1, name: '医神' },
    { id: 2, name: '赘婿' },
    { id: 3, name: '鉴宝' },
    { id: 4, name: '战神' },
    { id: 5, name: '娱乐明星' },
    { id: 6, name: '神医' },
    { id: 7, name: '重生' },
    { id: 8, name: '职场' },
    { id: 9, name: '逆袭' },
    { id: 10, name: '复仇' },
    { id: 11, name: '青春' },
    { id: 12, name: '官场' },
    { id: 13, name: '家庭情感' },
    { id: 14, name: '乡村' },
    { id: 15, name: '正能量' },
    { id: 16, name: '成长' },
    { id: 17, name: '伦理' },
    { id: 18, name: '都市情感' },
    { id: 19, name: '社会话题' },
    { id: 20, name: '灵异' },
    { id: 21, name: '悬疑推理' },
    { id: 22, name: '虐恋' },
    { id: 23, name: '甜宠' },
    { id: 24, name: '高干军婚' },
    { id: 25, name: '年代' },
    { id: 26, name: '萌宝' },
    { id: 27, name: '腹黑' },
    { id: 28, name: '总裁' },
    { id: 29, name: '宫斗宅斗' },
    { id: 30, name: '穿越' },
    { id: 31, name: '种田经商' },
    { id: 33, name: '民俗' },
    { id: 34, name: '古装' },
    { id: 35, name: '穿越战争' },
    { id: 36, name: '现代军事' },
    { id: 37, name: '奇幻' },
    { id: 38, name: '科幻' },
    { id: 39, name: '架空玄幻' },
    { id: 40, name: '热血' },
    { id: 41, name: '历史' },
    { id: 42, name: '搞笑' },
    { id: 43, name: '仙侠' },
    { id: 44, name: '武侠' },
    { id: 45, name: '二次元' },
    { id: 46, name: '其他' },
  ]
  await db.insert(schema.genres).values(genresList)
}

init().then(() => {
  console.log('seed done')
  process.exit(0)
})
