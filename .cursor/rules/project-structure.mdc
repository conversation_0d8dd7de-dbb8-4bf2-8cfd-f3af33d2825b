# 项目结构说明

本项目的主要入口文件为 [src/main.ts](mdc:src/main.ts)。

- 配置文件位于项目根目录（如 [app.config.ts](mdc:src/app.config.ts)）。
- 主要业务模块在 [src/modules/](mdc:src/modules/) 目录下，按功能分子目录。
- 公共工具、装饰器、中间件等在 [src/common/](mdc:src/common/)。
- 全局配置和日志相关在 [src/global/](mdc:src/global/)。
- 任务调度、TOS 文件服务等在 [src/shared/](mdc:src/shared/)。
- 数据库 schema 定义在 [src/common/drizzle/schema/](mdc:src/common/drizzle/schema/)。
- 测试代码在 [test/](mdc:test/) 目录。

## 项目技术栈
- Nest.js: https://docs.nestjs.com/
- DrizzleORM: https://orm.drizzle.team/docs
- pnpm: https://pnpm.io


## 要点
- 不要用 try catch 来捕获代码，因为全局有异常处理，需要处理时直接 throw new BizException
