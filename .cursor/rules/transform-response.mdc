# Database Response Transformation Rule

When returning database query results to the frontend, always use the `toResponse` function from [transform.util.ts](mdc:src/common/utils/transform.util.ts) to transform the data.

## Usage Pattern

```typescript
// ✅ Correct: Transform data before returning
const users = await this.userService.findAll();
return toResponse(UserResponse, users);

// ❌ Incorrect: Returning raw database data
const users = await this.userService.findAll();
return users;
```

## Function Signature

The `toResponse` function has the following overloads:
- `toResponse<T, V>(cls: new () => T, data: V[]): T[]`
- `toResponse<T, V>(cls: new () => T, data: V): T`

## Benefits

1. **Type Safety**: Ensures response data matches expected DTO structure
2. **Data Transformation**: Automatically handles property mapping and type conversion
3. **Consistency**: Maintains uniform response format across all endpoints
4. **Security**: Prevents exposing internal database fields to frontend

## Example Implementation

```typescript
// In your service layer
import { toResponse } from '@/common/utils/transform.util';
import { UserResponse } from './responses/user.response';

export class UserService {
  async findAll() {
    const users = await this.userRepository.findAll();
    return toResponse(UserResponse, users);
  }
  
  async findOne(id: string) {
    const user = await this.userRepository.findById(id);
    return toResponse(UserResponse, user);
  }
}
```

Always create corresponding response DTOs in the `responses` directory for each entity that will be returned to the frontend.
